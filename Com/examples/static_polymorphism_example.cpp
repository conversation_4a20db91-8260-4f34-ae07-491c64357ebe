//
// Created by 李浩楠 on 25-6-28.
// 静多态事件架构使用示例
//

#include "event/event_factory.hpp"
#include "log_system_guard.hpp"

#include <iostream>
#include <memory>
#include <string>

// 示例数据类型
struct RadarData
{
    double distance;
    double velocity;
    double angle;
    uint64_t timestamp;

    RadarData() : distance(0.0), velocity(0.0), angle(0.0), timestamp(0) {}
    RadarData(double d, double v, double a, uint64_t t) : 
        distance(d), velocity(v), angle(a), timestamp(t) {}
};

struct CameraData
{
    std::string image_path;
    uint32_t width;
    uint32_t height;
    uint64_t timestamp;

    CameraData() : width(0), height(0), timestamp(0) {}
    CameraData(const std::string& path, uint32_t w, uint32_t h, uint64_t t) :
        image_path(path), width(w), height(h), timestamp(t) {}
};

using namespace aether::com::network_binding::dds::proxy;
using namespace aether::com;

/**
 * @brief 演示DDS事件的使用
 */
void DemonstrateDDSEvent()
{
    std::cout << "\n=== DDS Event 示例 ===" << std::endl;

    // 创建服务实例信息
    ServiceInstanceInfo service_info("RadarService", "front_radar_01");

    // 创建DDS事件
    auto radar_event = EventFactory::CreateDDSEvent<RadarData>(
        service_info, 
        "RadarDataTopic"
    );

    // 设置事件处理器
    radar_event.SetReceiveHandler([]() {
        std::cout << "DDS: 新的雷达数据到达!" << std::endl;
    });

    radar_event.SetSubscriptionStateChangeHandler([](SubscriptionState state) {
        switch (state)
        {
            case SubscriptionState::kSubscribed:
                std::cout << "DDS: 雷达事件订阅成功" << std::endl;
                break;
            case SubscriptionState::kNotSubscribed:
                std::cout << "DDS: 雷达事件未订阅" << std::endl;
                break;
            case SubscriptionState::kSubscriptionPending:
                std::cout << "DDS: 雷达事件订阅中..." << std::endl;
                break;
        }
    });

    // 订阅事件
    auto result = radar_event.Subscribe(10);
    if (result.has_value())
    {
        std::cout << "DDS: 雷达事件订阅请求发送成功" << std::endl;
        std::cout << "DDS: 当前状态: " << static_cast<int>(radar_event.GetSubscriptionState()) << std::endl;
        std::cout << "DDS: 空闲样本槽: " << radar_event.GetFreeSampleCount() << std::endl;
    }
    else
    {
        std::cout << "DDS: 雷达事件订阅失败: " << result.error().error_message << std::endl;
    }

    // 模拟获取新样本
    auto samples_result = radar_event.GetNewSamples([](const SamplePtr<RadarData const>& sample) {
        std::cout << "DDS: 收到雷达数据 - 距离: " << sample->distance 
                  << "m, 速度: " << sample->velocity << "m/s" << std::endl;
    }, 5);

    if (samples_result.has_value())
    {
        std::cout << "DDS: 处理了 " << samples_result.value() << " 个样本" << std::endl;
    }

    // 取消订阅
    radar_event.Unsubscribe();
    std::cout << "DDS: 雷达事件已取消订阅" << std::endl;
}

/**
 * @brief 演示SOME/IP事件的使用
 */
void DemonstrateSomeIpEvent()
{
    std::cout << "\n=== SOME/IP Event 示例 ===" << std::endl;

    // 创建服务实例信息
    ServiceInstanceInfo service_info("CameraService", "front_camera_01");

    // 配置SOME/IP参数
    SomeIpConfig someip_config;
    someip_config.service_id = 0x1234;
    someip_config.instance_id = 0x5678;
    someip_config.eventgroup_id = 0x01;
    someip_config.event_id = 0x8001;
    someip_config.multicast_address = "*********";
    someip_config.multicast_port = 30490;

    // 创建SOME/IP事件
    auto camera_event = EventFactory::CreateSomeIpEvent<CameraData>(
        service_info,
        "CameraDataTopic",  // 在SOME/IP中可能不直接使用
        someip_config
    );

    // 设置事件处理器
    camera_event.SetReceiveHandler([]() {
        std::cout << "SOME/IP: 新的相机数据到达!" << std::endl;
    });

    camera_event.SetSubscriptionStateChangeHandler([](SubscriptionState state) {
        switch (state)
        {
            case SubscriptionState::kSubscribed:
                std::cout << "SOME/IP: 相机事件订阅成功" << std::endl;
                break;
            case SubscriptionState::kNotSubscribed:
                std::cout << "SOME/IP: 相机事件未订阅" << std::endl;
                break;
            case SubscriptionState::kSubscriptionPending:
                std::cout << "SOME/IP: 相机事件订阅中..." << std::endl;
                break;
        }
    });

    // 订阅事件
    auto result = camera_event.Subscribe(5);
    if (result.has_value())
    {
        std::cout << "SOME/IP: 相机事件订阅请求发送成功" << std::endl;
        std::cout << "SOME/IP: 当前状态: " << static_cast<int>(camera_event.GetSubscriptionState()) << std::endl;
        std::cout << "SOME/IP: 空闲样本槽: " << camera_event.GetFreeSampleCount() << std::endl;
    }
    else
    {
        std::cout << "SOME/IP: 相机事件订阅失败: " << result.error().error_message << std::endl;
    }

    // 模拟获取新样本
    auto samples_result = camera_event.GetNewSamples([](const SamplePtr<CameraData const>& sample) {
        std::cout << "SOME/IP: 收到相机数据 - 图像: " << sample->image_path 
                  << ", 尺寸: " << sample->width << "x" << sample->height << std::endl;
    }, 3);

    if (samples_result.has_value())
    {
        std::cout << "SOME/IP: 处理了 " << samples_result.value() << " 个样本" << std::endl;
    }

    // 取消订阅
    camera_event.Unsubscribe();
    std::cout << "SOME/IP: 相机事件已取消订阅" << std::endl;
}

/**
 * @brief 演示协议无关的事件使用
 */
void DemonstrateProtocolAgnosticUsage()
{
    std::cout << "\n=== 协议无关使用示例 ===" << std::endl;

    ServiceInstanceInfo service_info("SensorService", "multi_sensor_01");

    // 根据配置动态选择协议
    ComProtocol selected_protocol = ComProtocol::DDS;  // 可以从配置文件读取

    // 创建事件（协议在运行时确定）
    auto sensor_event = EventFactory::CreateEvent<RadarData>(
        selected_protocol,
        service_info,
        "SensorDataTopic"
    );

    if (!sensor_event.IsInitialized())
    {
        std::cout << "协议无关: 事件创建失败" << std::endl;
        return;
    }

    std::cout << "协议无关: 使用 " << (selected_protocol == ComProtocol::DDS ? "DDS" : "SOME/IP") 
              << " 协议创建事件" << std::endl;

    // 统一的接口使用，无论底层协议如何
    sensor_event.SetReceiveHandler([]() {
        std::cout << "协议无关: 传感器数据到达!" << std::endl;
    });

    auto result = sensor_event.Subscribe(8);
    if (result.has_value())
    {
        std::cout << "协议无关: 传感器事件订阅成功" << std::endl;
        std::cout << "协议无关: 空闲样本槽: " << sensor_event.GetFreeSampleCount() << std::endl;
    }

    // 无论底层协议如何，使用相同的接口
    auto samples_result = sensor_event.GetNewSamples([](const SamplePtr<RadarData const>& sample) {
        std::cout << "协议无关: 收到传感器数据" << std::endl;
    });

    sensor_event.Unsubscribe();
    std::cout << "协议无关: 传感器事件已取消订阅" << std::endl;
}

/**
 * @brief 演示多协议混合使用
 */
void DemonstrateMixedProtocolUsage()
{
    std::cout << "\n=== 多协议混合使用示例 ===" << std::endl;

    // 同时使用DDS和SOME/IP协议的不同事件
    ServiceInstanceInfo dds_service("DDSService", "dds_instance");
    ServiceInstanceInfo someip_service("SomeIpService", "someip_instance");

    auto dds_event = EventFactory::CreateDDSEvent<RadarData>(dds_service, "DDSRadarTopic");
    auto someip_event = EventFactory::CreateSomeIpEvent<CameraData>(someip_service, "SomeIpCameraTopic");

    // 可以同时管理不同协议的事件
    std::vector<std::function<void()>> event_operations;

    event_operations.push_back([&dds_event]() {
        auto result = dds_event.Subscribe(5);
        std::cout << "混合使用: DDS事件订阅 " << (result.has_value() ? "成功" : "失败") << std::endl;
    });

    event_operations.push_back([&someip_event]() {
        auto result = someip_event.Subscribe(3);
        std::cout << "混合使用: SOME/IP事件订阅 " << (result.has_value() ? "成功" : "失败") << std::endl;
    });

    // 执行所有操作
    for (auto& operation : event_operations)
    {
        operation();
    }

    std::cout << "混合使用: 同时管理了 DDS 和 SOME/IP 事件" << std::endl;
}

int main()
{
    // 配置并初始化日志系统
    aether::Utils::log::LogConfig config;
    config.level = aether::Utils::log::LogLevel::Debug;
    aether::Utils::log::LogSystemGuard logGuard(config);

    std::cout << "静多态事件架构演示" << std::endl;
    std::cout << "===================" << std::endl;

    try
    {
        // 演示不同协议的事件使用
        DemonstrateDDSEvent();
        DemonstrateSomeIpEvent();
        DemonstrateProtocolAgnosticUsage();
        DemonstrateMixedProtocolUsage();

        std::cout << "\n演示完成!" << std::endl;
    }
    catch (const std::exception& e)
    {
        std::cerr << "演示过程中发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
