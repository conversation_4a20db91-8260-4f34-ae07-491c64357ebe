#ifndef COM_ERROR_DOMAIN_HPP
#define COM_ERROR_DOMAIN_HPP
#include <stdexcept>
#include <string>
#include <string_view>

#include "../../Core/error_code.hpp"
#include "../../Core/error_domain.hpp"
#include "../../Core/error_types.hpp"

namespace ara::com
{
    enum class ComErrorCode : core::ErrorCodeType
    {
        // Implementation - [SWS_CM_10432]
        kServiceNotAvailable = 1,
        kMaxSamplesExceeded = 2,
        kNetworkBindingFailure = 3,
        kGrantEnforcementError = 4,
        kPeerIsUnreachable = 5,
        kFieldValueIsNotValid = 6,
        kFieldSetHandlerNotSet = 7,
        kUnsetHandlerFailure = 8,
        kSampleAllocationFailure = 9,
        kIllegalUseOfAllocate = 10,
        kServiceNotOffered = 11,
        kInstanceIDCouldNotBeResolved = 15,
        kMaxSampleCountNotRealizable = 16,
        kWrongMethodCallProcessingMode = 17,
        kErroneousFileHandle = 18,
        kCouldNotExecute = 19,
        kInvalidInstanceIdentifierString = 20,
        kSetHandlerFailure = 21,

        //vendor specific error codes
        kDDSParticipantCreationFailure = 51,
        kDDSParticipantUnavailable = 52,

        kDDSSubscriberCreationFailure = 53,
        kDDSSubscriberUnavailable = 54,

        kDDSTopicCreationFailure = 55,
        kDDSTopicUnavailable = 56,

        kDDSDataReaderCreationFailure = 57,
        kDDSDataReaderUnavailable = 58,

        kDDSDataReaderListenerCreationFailure = 59
    };

    // Implementation - [SWS_CM_11334]
    class ComErrorDomain final : public core::ErrorDomain
    {
    public:
        friend constexpr const ComErrorDomain& GetComErrorDomain() noexcept;

        // Implementation - [SWS_CM_11330] -Constructs a new ComErrorDomain object - Not allowed
        ComErrorDomain() = delete;

        [[nodiscard]] std::string_view Name() const noexcept override { return "ComErrorDomain"; }

        [[nodiscard]] std::string_view Message(CodeType errorCode) const noexcept override
        {
            thread_local static std::string cached_message;

            switch (static_cast<ComErrorCode>(errorCode))
            {
                case ComErrorCode::kMaxSampleCountNotRealizable:
                {
                    cached_message = "Maximum sample count not realizable";
                }
                default:
                {
                    cached_message = "Unknown COM error: " + std::to_string(errorCode);
                }
            }
            return cached_message;
        }

        void ThrowAsException(const core::ErrorCode& errorCode) const override
        {
            std::string error_msg = "ComErrorDomain exception: ";
            error_msg += Message(errorCode.Value());
            throw std::runtime_error(error_msg);
        }

    private:
        // 私有有参构造函数，使用 ComErrorDomain 的 ID
        explicit constexpr ComErrorDomain(Idtype id) noexcept :
            ErrorDomain(id)
        {
        }
    };


    // Implementation - [SWS_CM_11334]
    constexpr const ComErrorDomain& GetComErrorDomain() noexcept
    {
        static constexpr ComErrorDomain domain{core::ComErrorDomain};
        return domain;
    }


    // Implementation - [SWS_CM_11335] - ADL
    constexpr core::ErrorCode MakeErrorCode(com::ComErrorCode code, core::ErrorDomain::SupportDataType data) noexcept
    {
        return core::ErrorCode(static_cast<core::ErrorCodeType>(code), GetComErrorDomain(), data);
    }
} // namespace ara::com


#endif
