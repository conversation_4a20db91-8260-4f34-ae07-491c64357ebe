#ifndef DDS_QOS_WRAPPER_HPP_
#define DDS_QOS_WRAPPER_HPP_

#include <fastdds/dds/core/policy/QosPolicies.hpp>
#include <fastdds/dds/domain/qos/DomainParticipantQos.hpp>
#include <fastdds/dds/publisher/qos/PublisherQos.hpp>
#include <fastdds/dds/subscriber/qos/DataReaderQos.hpp>
#include <fastdds/dds/subscriber/qos/SubscriberQos.hpp>
#include <fastdds/dds/topic/qos/TopicQos.hpp>


using eprosima::fastdds::dds::DomainParticipantQos;
using eprosima::fastdds::dds::UserDataQosPolicy;
using eprosima::fastdds::dds::DataReaderQos;    
using eprosima::fastdds::dds::PublisherQos;
using eprosima::fastdds::dds::SubscriberQos;
using eprosima::fastdds::dds::TopicQos;

namespace ara::com::network_binding::proxy::dds{

    struct ProxyQosSettings {
        DomainParticipantQos participant_qos;
        DataReaderQos reader_qos;
        SubscriberQos subscriber_qos;
        TopicQos topic_qos;
    };



}


#endif // DDS_QOS_WRAPPER_HPP_