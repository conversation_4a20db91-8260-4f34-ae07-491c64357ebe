//
// Created by 李浩楠 on 25-6-28.
//

#ifndef EVENT_FACTORY_HPP
#define EVENT_FACTORY_HPP

#include "event_interface.hpp"
#include "dds_event.hpp"
#include "com_error_domain.hpp"

#include <memory>
#include <variant>

namespace aether::com::network_binding::proxy
{
    /**
     * @brief 事件类型变体 - 支持不同协议的事件实现
     * @tparam SampleType 事件数据类型
     */
    template <typename SampleType>
    using EventVariant = std::variant<
        std::unique_ptr<DDSEvent<SampleType>>,
        std::unique_ptr<SomeIpEvent<SampleType>>
    >;

    /**
     * @brief 统一的事件包装器 - 提供协议无关的接口
     * @tparam SampleType 事件数据类型
     */
    template <typename SampleType>
    class Event
    {
    public:
        /**
         * @brief 默认构造函数
         */
        Event() = default;

        /**
         * @brief 移动构造函数
         */
        Event(EventVariant<SampleType>&& event_impl) : event_impl_(std::move(event_impl)) {}

        /**
         * @brief 移动赋值操作符
         */
        Event& operator=(EventVariant<SampleType>&& event_impl)
        {
            event_impl_ = std::move(event_impl);
            return *this;
        }

        /**
         * @brief 订阅事件
         * @param maxSampleCount 最大样本数量
         * @return 订阅结果
         */
        core::Result<void> Subscribe(size_t maxSampleCount)
        {
            return std::visit([maxSampleCount](auto& impl) -> core::Result<void> {
                if (impl)
                {
                    return impl->Subscribe(maxSampleCount);
                }
                return core::MakeErrorInfo(core::ErrorDomain::com,
                                           static_cast<core::ErrorCode>(ComErrc::kServiceNotOffered),
                                           "Event implementation not initialized");
            }, event_impl_);
        }

        /**
         * @brief 取消订阅
         */
        void Unsubscribe()
        {
            std::visit([](auto& impl) {
                if (impl)
                {
                    impl->Unsubscribe();
                }
            }, event_impl_);
        }

        /**
         * @brief 获取新的事件样本
         * @param f 回调函数，接收SamplePtr<SampleType const>参数
         * @param maxNumberOfSamples 可选参数，指定最多处理的样本数量
         * @return 实际处理的样本数量，或错误码
         */
        template <typename Callable>
        core::Result<size_t> GetNewSamples(Callable&& f, size_t maxNumberOfSamples = SIZE_MAX)
        {
            // 使用泛型lambda保持完美转发
            return std::visit([maxNumberOfSamples](auto& impl, auto&& func) -> core::Result<size_t> {
                if (impl)
                {
                    return impl->GetNewSamples(std::forward<decltype(func)>(func), maxNumberOfSamples);
                }
                return core::MakeErrorInfo(core::ErrorDomain::com,
                                           static_cast<core::ErrorCode>(ComErrc::kServiceNotOffered),
                                           "Event implementation not initialized");
            }, event_impl_, std::forward<Callable>(f));
        }

        /**
         * @brief 获取空闲样本槽数量
         * @return 可用的样本槽数量
         */
        size_t GetFreeSampleCount() const
        {
            return std::visit([](const auto& impl) -> size_t {
                if (impl)
                {
                    return impl->GetFreeSampleCount();
                }
                return 0;
            }, event_impl_);
        }

        /**
         * @brief 设置接收处理器（事件驱动模式）
         * @param handler 当有新事件数据到达时调用的处理器
         */
        void SetReceiveHandler(EventReceiveHandler handler)
        {
            std::visit([&handler](auto& impl) {
                if (impl)
                {
                    impl->SetReceiveHandler(std::move(handler));
                }
            }, event_impl_);
        }

        /**
         * @brief 取消设置接收处理器
         */
        void UnsetReceiveHandler()
        {
            std::visit([](auto& impl) {
                if (impl)
                {
                    impl->UnsetReceiveHandler();
                }
            }, event_impl_);
        }

        /**
         * @brief 设置订阅状态变化处理器
         * @param handler 当订阅状态发生变化时调用的处理器
         */
        void SetSubscriptionStateChangeHandler(SubscriptionStateChangeHandler handler)
        {
            std::visit([&handler](auto& impl) {
                if (impl)
                {
                    impl->SetSubscriptionStateChangeHandler(std::move(handler));
                }
            }, event_impl_);
        }

        /**
         * @brief 取消设置订阅状态变化处理器
         */
        void UnsetSubscriptionStateChangeHandler()
        {
            std::visit([](auto& impl) {
                if (impl)
                {
                    impl->UnsetSubscriptionStateChangeHandler();
                }
            }, event_impl_);
        }

        /**
         * @brief 获取当前订阅状态
         * @return 当前订阅状态
         */
        SubscriptionState GetSubscriptionState() const
        {
            return std::visit([](const auto& impl) -> SubscriptionState {
                if (impl)
                {
                    return impl->GetSubscriptionState();
                }
                return SubscriptionState::kNotSubscribed;
            }, event_impl_);
        }

        /**
         * @brief 检查是否已订阅
         * @return 如果已订阅返回true，否则返回false
         */
        bool IsSubscribed() const
        {
            return std::visit([](const auto& impl) -> bool {
                if (impl)
                {
                    return impl->IsSubscribed();
                }
                return false;
            }, event_impl_);
        }

        /**
         * @brief 获取当前订阅的最大样本数量
         * @return 当前最大样本数量，如果未订阅则返回0
         */
        size_t GetCurrentMaxSampleCount() const
        {
            return std::visit([](const auto& impl) -> size_t {
                if (impl)
                {
                    return impl->GetCurrentMaxSampleCount();
                }
                return 0;
            }, event_impl_);
        }

        /**
         * @brief 检查事件是否已初始化
         * @return 如果已初始化返回true，否则返回false
         */
        [[nodiscard]] bool IsInitialized() const
        {
            return std::visit([](const auto& impl) -> bool {
                return static_cast<bool>(impl);
            }, event_impl_);
        }

    private:
        EventVariant<SampleType> event_impl_;
    };

    /**
     * @brief 事件工厂类 - 根据协议类型创建相应的事件实现
     */
    class EventFactory
    {
    public:
        /**
         * @brief 创建DDS事件
         * @tparam SampleType 事件数据类型
         * @param service_info 服务实例信息
         * @param topic_name 事件对应的Topic名称
         * @param qos_config DDS QoS配置
         * @return DDS事件实例
         */
        template <typename SampleType>
        static Event<SampleType> CreateDDSEvent(
            const ServiceInstanceInfo& service_info,
            const std::string& topic_name,
            const ProxyQosSettings& qos_config = ProxyQosSettings())
        {
            auto dds_event = std::make_unique<DDSEvent<SampleType>>(service_info, topic_name, qos_config);
            EventVariant<SampleType> variant = std::move(dds_event);
            return Event<SampleType>(std::move(variant));
        }

        /**
         * @brief 创建SOME/IP事件
         * @tparam SampleType 事件数据类型
         * @param service_info 服务实例信息
         * @param topic_name 事件对应的Topic名称（在SOME/IP中可能不直接使用）
         * @param someip_config SOME/IP特定配置
         * @return SOME/IP事件实例
         */
        template <typename SampleType>
        static Event<SampleType> CreateSomeIpEvent(
            const ServiceInstanceInfo& service_info,
            const std::string& topic_name,
            const SomeIpConfig& someip_config = SomeIpConfig())
        {
            auto someip_event = std::make_unique<SomeIpEvent<SampleType>>(service_info, topic_name, someip_config);
            EventVariant<SampleType> variant = std::move(someip_event);
            return Event<SampleType>(std::move(variant));
        }
    };

}  // namespace aether::com::network_binding::dds::proxy

#endif  // EVENT_FACTORY_HPP
