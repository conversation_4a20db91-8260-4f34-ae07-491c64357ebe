//
// Created by 李浩楠 on 25-6-28.
//

#ifndef EVENT_INTERFACE_HPP
#define EVENT_INTERFACE_HPP

#include <algorithm>
#include <atomic>
#include <cstddef>
#include <cstdint>
#include <deque>
#include <functional>
#include <memory>
#include <string>
#include <mutex>
#include <utility>
#include "com_error_domain.hpp"
#include "result.hpp"


namespace ara::com::network_binding::dds::proxy
{
    // 事件接收处理器类型
    using EventReceiveHandler = std::function<void()>;

    // 订阅状态枚举
    enum class SubscriptionState
    {
        kSubscribed, // 表示订阅已成功建立并处于活动状态
        kNotSubscribed, // 表示尚未订阅，或者订阅已被取消
        kSubscriptionPending // 表示订阅请求已被接受，但实际的订阅过程仍在进行中
    };

    // 订阅状态变化处理器类型
    using SubscriptionStateChangeHandler = std::function<void(SubscriptionState)>;

    // 服务实例信息
    struct ServiceInstanceInfo
    {
        std::string service_id; // 服务ID
        std::string instance_id; // 实例ID
        std::string partition_name; // 分区名称

        ServiceInstanceInfo(const std::string& svc_id, const std::string& inst_id) :
            service_id(svc_id), instance_id(inst_id)
        {
            partition_name = "ara.com://services/" + service_id + "_" + instance_id;
        }
    };

    /**
     * @brief 事件接口基类 - 使用CRTP实现静多态
     * @tparam Derived 派生类类型（DDS或SOMEIP实现）
     * @tparam SampleType 事件数据类型
     */
    template <typename Derived, typename SampleType>
    class Event
    {
    public:
        /**
         * @brief 构造函数
         * @param service_info 服务实例信息
         * @param topic_name 事件对应的Topic名称
         */
        explicit Event(const ServiceInstanceInfo& service_info, const std::string& topic_name) :
            service_info_(service_info), topic_name_(topic_name),
            subscription_state_(SubscriptionState::kNotSubscribed),
            current_max_sample_count_(0), allocated_sample_count_(0)
        {
        }
        
        ~Event() noexcept
        {
            if (IsSubscribed())
            {
                static_cast<Derived*>(this)->Unsubscribe();
            }
        }

        // 禁用拷贝和移动，确保RAII正确性
        Event(const Event&) = delete;
        Event& operator=(const Event&) = delete;
        Event(Event&&) = delete;
        Event& operator=(Event&&) = delete;

        /**
         * @brief 订阅事件
         * @param maxSampleCount 最大样本数量
         * @return 订阅结果
         */
        ara::core::Result<void> Subscribe(size_t maxSampleCount)
        {
            std::lock_guard<std::mutex> lock(state_mutex_);

            // 原子地检查当前状态
            auto current_state = subscription_state_.load(std::memory_order_acquire);
            if (current_state != SubscriptionState::kNotSubscribed)
            {
                if (current_max_sample_count_.load(std::memory_order_relaxed) == maxSampleCount)
                {
                    return core::Ok(); // 成功，无需操作
                }
                else
                {
                    return core::Err(ComErrorCode::kServiceNotOffered);
                }
            }

            // 设置为pending状态
            subscription_state_.store(SubscriptionState::kSubscriptionPending, std::memory_order_release);

            // 调用派生类的具体实现
            auto result = static_cast<Derived*>(this)->Subscribe(maxSampleCount);
            if (result.has_value())
            {
                // 原子地更新所有状态
                current_max_sample_count_.store(maxSampleCount, std::memory_order_relaxed);
                allocated_sample_count_.store(0, std::memory_order_relaxed);
                sample_buffer_.clear();
                subscription_state_.store(SubscriptionState::kSubscribed, std::memory_order_release);
                NotifySubscriptionStateChange(SubscriptionState::kSubscribed);
            }
            else
            {
                // 失败时恢复状态
                subscription_state_.store(SubscriptionState::kNotSubscribed, std::memory_order_release);
            }
            return result;
        }

        /**
         * @brief 取消订阅
         */
        void Unsubscribe() noexcept
        {
            std::lock_guard<std::mutex> lock(state_mutex_);

            auto current_state = subscription_state_.load(std::memory_order_acquire);
            if (current_state != SubscriptionState::kNotSubscribed)
            {
                try
                {
                    // 调用派生类的具体实现
                    static_cast<Derived*>(this)->Unsubscribe();
                }
                catch (...)
                {
                    // 即使派生类实现失败，也要重置状态
                }

                // 原子地重置状态
                current_max_sample_count_.store(0, std::memory_order_relaxed);
                allocated_sample_count_.store(0, std::memory_order_relaxed);
                sample_buffer_.clear();
                subscription_state_.store(SubscriptionState::kNotSubscribed, std::memory_order_release);

                // 通知状态变化
                try
                {
                    NotifySubscriptionStateChange(SubscriptionState::kNotSubscribed);
                }
                catch (...)
                {
                    // 通知失败不应影响取消订阅操作
                }
            }
        }

        /**
         * @brief 获取新的事件样本
         * @param f 回调函数，接收SamplePtr<SampleType const>参数
         * @param maxNumberOfSamples 可选参数，指定最多处理的样本数量
         * @return 实际处理的样本数量，或错误码
         */
        template <typename Callable>
        core::Result<size_t> GetNewSamples(Callable&& f, size_t maxNumberOfSamples = SIZE_MAX)
        {
            if (!is_subscribed_)
            {
                return core::MakeErrorInfo(core::ErrorDomain::com,
                                           static_cast<core::ErrorCode>(ComErrc::kServiceNotOffered),
                                           "Event is not subscribed");
            }

            // 检查是否超过最大样本数量限制
            if (allocated_sample_count_ >= current_max_sample_count_)
            {
                return core::MakeErrorInfo(core::ErrorDomain::com,
                                           static_cast<core::ErrorCode>(ComErrc::kMaxSamplesExceeded),
                                           "Maximum sample count exceeded");
            }

            // 调用派生类的具体实现获取样本
            auto samples = static_cast<Derived*>(this)->DoGetNewSamples(
                std::min(maxNumberOfSamples, current_max_sample_count_ - allocated_sample_count_));

            size_t processedCount = 0;
            for (auto& sample : samples)
            {
                if (sample && processedCount < maxNumberOfSamples)
                {
                    // 创建SamplePtr并调用回调函数
                    auto deleter = [this]()
                    {
                        if (allocated_sample_count_ > 0)
                        {
                            allocated_sample_count_--;
                        }
                    };

                    SamplePtr<SampleType const> samplePtr(sample, deleter);
                    f(samplePtr);
                    allocated_sample_count_++;
                    processedCount++;
                }
            }

            return processedCount;
        }

        /**
         * @brief 获取空闲样本槽数量
         * @return 可用的样本槽数量
         */
        size_t GetFreeSampleCount() const
        {
            if (!is_subscribed_)
            {
                return 0;
            }
            return current_max_sample_count_ - allocated_sample_count_;
        }

        /**
         * @brief 设置接收处理器（事件驱动模式）
         * @param handler 当有新事件数据到达时调用的处理器
         */
        void SetReceiveHandler(EventReceiveHandler handler)
        {
            receive_handler_ = std::move(handler);
        }

        /**
         * @brief 取消设置接收处理器
         */
        void UnsetReceiveHandler()
        {
            receive_handler_ = nullptr;
        }

        /**
         * @brief 设置订阅状态变化处理器
         * @param handler 当订阅状态发生变化时调用的处理器
         */
        void SetSubscriptionStateChangeHandler(SubscriptionStateChangeHandler handler)
        {
            subscription_state_handler_ = std::move(handler);
        }

        /**
         * @brief 取消设置订阅状态变化处理器
         */
        void UnsetSubscriptionStateChangeHandler()
        {
            subscription_state_handler_ = nullptr;
        }

        /**
         * @brief 获取当前订阅状态
         * @return 当前订阅状态
         */
        SubscriptionState GetSubscriptionState() const
        {
            return static_cast<const Derived*>(this)->DoGetSubscriptionState();
        }

        /**
         * @brief 检查是否已订阅 - 线程安全版本
         * @return 如果已订阅返回true，否则返回false
         */
        bool IsSubscribed() const noexcept
        {
            return subscription_state_.load(std::memory_order_acquire) == SubscriptionState::kSubscribed;
        }

        /**
         * @brief 获取当前订阅的最大样本数量
         * @return 当前最大样本数量，如果未订阅则返回0
         */
        size_t GetCurrentMaxSampleCount() const { return current_max_sample_count_; }

    protected:
        // 基本配置
        ServiceInstanceInfo service_info_; // 服务实例信息
        std::string topic_name_; // Topic名称

        // 订阅状态管理 - 线程安全
        mutable std::mutex state_mutex_; // 状态保护互斥锁
        std::atomic<SubscriptionState> subscription_state_; // 订阅状态（原子操作）
        std::atomic<size_t> current_max_sample_count_; // 当前订阅的最大样本数量
        std::atomic<size_t> allocated_sample_count_; // 当前已分配的样本数量

        // 事件驱动模式支持
        EventReceiveHandler receive_handler_;
        SubscriptionStateChangeHandler subscription_state_handler_;

        // FIFO样本缓冲区
        std::deque<std::shared_ptr<SampleType>> sample_buffer_;

        /**
         * @brief 通知订阅状态变化
         * @param newState 新的订阅状态
         */
        void NotifySubscriptionStateChange(SubscriptionState newState)
        {
            if (subscription_state_handler_)
            {
                subscription_state_handler_(newState);
            }
        }

        /**
         * @brief 触发接收处理器
         */
        void TriggerReceiveHandler()
        {
            if (receive_handler_)
            {
                receive_handler_();
            }
        }
    };
} // namespace aether::com::network_binding::dds::proxy

#endif  // EVENT_INTERFACE_HPP
