//
// Created by 李浩楠 on 25-6-28.
//

#ifndef EVENT_INTERFACE_HPP
#define EVENT_INTERFACE_HPP

#include <cstddef>
#include <cstdint>
#include <functional>
#include <mutex>
#include "event_types.hpp"
#include "result.hpp"


namespace ara::com::network_binding::proxy
{
    // 事件接收处理器类型
    using EventReceiveHandler = std::function<void()>;



    // 订阅状态变化处理器类型
    using SubscriptionStateChangeHandler = std::function<void(SubscriptionState)>;

    /**
     * @brief 事件接口基类 - 使用CRTP实现静多态
     * @tparam Derived 派生类类型（DDS或SOMEIP实现）
     * @tparam SampleType 事件数据类型
     */
    template <typename Derived, typename SampleType>
    class Event
    {
    public:
        /**
         * @brief 默认构造函数 - 基类只处理通用初始化
         * 派生类负责处理协议特定的参数
         */
        Event() = default;

        
        ~Event() noexcept
        {
            UnSubscribe();
        }

        // 禁用拷贝和移动，确保RAII正确性
        Event(const Event&) = delete;
        Event& operator=(const Event&) = delete;
        Event(Event&&) = delete;
        Event& operator=(Event&&) = delete;

        /**
         * @brief 订阅事件
         * @param maxSampleCount 最大样本数量
         * @return 订阅结果
         */
        ara::core::Result<void> Subscribe(size_t maxSampleCount)
        {   
            std::lock_guard<std::mutex> lock(state_mutex_);
            return static_cast<Derived*>(this)->Subscribe(maxSampleCount);
        }

        /**
         * @brief 取消订阅
         */
        void UnSubscribe() noexcept
        {
            std::lock_guard<std::mutex> lock(state_mutex_);
            static_cast<Derived*>(this)->UnSubscribe();
            
        }

        /**
         * @brief 获取新的事件样本
         * @param f 回调函数，接收SamplePtr<SampleType const>参数
         * @param maxNumberOfSamples 可选参数，指定最多处理的样本数量
         * @return 实际处理的样本数量，或错误码
         */
        template <typename Callable>
        core::Result<size_t> GetNewSamples(Callable&& f, size_t maxNumberOfSamples = SIZE_MAX)
        {
            
        }

        /**
         * @brief 获取空闲样本槽数量
         * @return 可用的样本槽数量
         */
        size_t GetFreeSampleCount() const
        {

        }

        /**
         * @brief 设置接收处理器（事件驱动模式）
         * @param handler 当有新事件数据到达时调用的处理器
         */
        void SetReceiveHandler(EventReceiveHandler handler)
        {
            
        }

        /**
         * @brief 取消设置接收处理器
         */
        void UnsetReceiveHandler()
        {
            
        }

        /**
         * @brief 设置订阅状态变化处理器
         * @param handler 当订阅状态发生变化时调用的处理器
         */
        void SetSubscriptionStateChangeHandler(SubscriptionStateChangeHandler handler)
        {
            
        }

        /**
         * @brief 取消设置订阅状态变化处理器
         */
        void UnsetSubscriptionStateChangeHandler()
        {
            
        }

        /**
         * @brief 获取当前订阅状态
         * @return 当前订阅状态
         */
        SubscriptionState GetSubscriptionState() const
        {
            return static_cast<const Derived*>(this)->GetSubscriptionState();
        }

        /**
         * @brief 获取当前订阅的最大样本数量
         * @return 当前最大样本数量，如果未订阅则返回0
         */
        size_t GetCurrentMaxSampleCount() const { 

         }

    protected:

        std::mutex state_mutex_;


        // FIFO样本缓冲区
        // std::deque<std::shared_ptr<SampleType>> sample_buffer_;


    };
} // namespace aether::com::network_binding::dds::proxy

#endif  // EVENT_INTERFACE_HPP
