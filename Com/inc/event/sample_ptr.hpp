#ifndef SAMPLE_PTR_HPP
#define SAMPLE_PTR_HPP

#include <memory>
#include <functional>

namespace aether::com {

    // Forward declaration for E2E profile check status
    namespace e2e {
        enum class ProfileCheckStatus {
            kOk,
            kError,
            kNotAvailable,
            kNoNewData
        };
    }

    /**
     * @brief 智能指针类，用于管理事件样本的生命周期
     * @tparam T 样本数据类型
     * 
     * 根据AUTOSAR AP规范要求：
     * - SamplePtr语义类似于std::unique_ptr，表示独占所有权
     * - 内存由ara::com实现分配和管理
     * - 当SamplePtr被销毁时，通知ara::com实现回收内存槽
     * - 禁止拷贝，仅支持移动语义
     * - 必须提供E2E保护检查状态访问
     */
    template<typename T>
    class SamplePtr {
    public:
        using element_type = T;
        using pointer = T*;
        using reference = T&;

        /**
         * @brief 默认构造函数，创建空的SamplePtr
         */
        SamplePtr() noexcept : ptr_(nullptr), sample_id_(0), com_instance_(nullptr), e2e_status_(e2e::ProfileCheckStatus::kNotAvailable) {}

        /**
         * @brief nullptr构造函数
         */
        constexpr SamplePtr(std::nullptr_t) noexcept : ptr_(nullptr), sample_id_(0), com_instance_(nullptr), e2e_status_(e2e::ProfileCheckStatus::kNotAvailable) {}

        /**
         * @brief 构造函数，从原始指针和通信实例创建SamplePtr
         * @param ptr 指向样本数据的指针
         * @param sample_id 样本在内存池中的ID
         * @param com_instance 通信管理实例指针
         * @param e2e_status E2E检查状态
         */
        explicit SamplePtr(T* ptr, std::size_t sample_id, void* com_instance, e2e::ProfileCheckStatus e2e_status = e2e::ProfileCheckStatus::kOk) noexcept
            : ptr_(ptr), sample_id_(sample_id), com_instance_(com_instance), e2e_status_(e2e_status) {}

        /**
         * @brief 移动构造函数
         */
        SamplePtr(SamplePtr&& other) noexcept
            : ptr_(other.ptr_), sample_id_(other.sample_id_), com_instance_(other.com_instance_), e2e_status_(other.e2e_status_) {
            other.ptr_ = nullptr;
            other.sample_id_ = 0;
            other.com_instance_ = nullptr;
            other.e2e_status_ = e2e::ProfileCheckStatus::kNotAvailable;
        }

        /**
         * @brief 移动赋值操作符
         */
        SamplePtr& operator=(SamplePtr&& other) noexcept {
            if (this != &other) {
                reset();
                ptr_ = other.ptr_;
                sample_id_ = other.sample_id_;
                com_instance_ = other.com_instance_;
                e2e_status_ = other.e2e_status_;
                other.ptr_ = nullptr;
                other.sample_id_ = 0;
                other.com_instance_ = nullptr;
                other.e2e_status_ = e2e::ProfileCheckStatus::kNotAvailable;
            }
            return *this;
        }

        /**
         * @brief nullptr赋值操作符
         */
        SamplePtr& operator=(std::nullptr_t) noexcept {
            reset();
            return *this;
        }

        /**
         * @brief 禁用拷贝构造和拷贝赋值
         * SamplePtr应该是独占的，不允许拷贝
         */
        SamplePtr(const SamplePtr&) = delete;
        SamplePtr& operator=(const SamplePtr&) = delete;

        /**
         * @brief 析构函数，自动通知ara::com回收内存
         */
        ~SamplePtr() noexcept {
            reset();
        }

        /**
         * @brief 解引用操作符
         * @return 样本数据的引用
         */
        reference operator*() const noexcept {
            return *ptr_;
        }

        /**
         * @brief 成员访问操作符
         * @return 指向样本数据的指针
         */
        pointer operator->() const noexcept {
            return ptr_;
        }

        /**
         * @brief 获取原始指针
         * @return 指向样本数据的原始指针
         */
        pointer Get() const noexcept {
            return ptr_;
        }

        /**
         * @brief 检查SamplePtr是否为空
         * @return 如果为空返回true，否则返回false
         */
        bool operator!() const noexcept {
            return ptr_ == nullptr;
        }

        /**
         * @brief 布尔转换操作符
         * @return 如果不为空返回true，否则返回false
         */
        explicit operator bool() const noexcept {
            return ptr_ != nullptr;
        }

        /**
         * @brief 重置SamplePtr，通知ara::com回收内存槽
         */
        void reset() noexcept {
            if (ptr_ != nullptr && com_instance_ != nullptr) {
                // 通知ara::com实现回收内存槽
                // 在实际实现中，这里需要调用具体的ara::com内存管理接口
                // 例如：static_cast<ComInstance*>(com_instance_)->ReleaseSampleSlot(sample_id_);
                ReleaseSampleSlot(com_instance_, sample_id_);
            }
            ptr_ = nullptr;
            sample_id_ = 0;
            com_instance_ = nullptr;
            e2e_status_ = e2e::ProfileCheckStatus::kNotAvailable;
        }

        /**
         * @brief 重置SamplePtr为nullptr
         */
        void Reset(std::nullptr_t) noexcept {
            reset();
        }

        /**
         * @brief 交换两个SamplePtr的内容
         * @param other 另一个SamplePtr
         */
        void Swap(SamplePtr& other) noexcept {
            std::swap(ptr_, other.ptr_);
            std::swap(sample_id_, other.sample_id_);
            std::swap(com_instance_, other.com_instance_);
            std::swap(e2e_status_, other.e2e_status_);
        }

        /**
         * @brief 获取E2E保护检查状态
         * @return E2E检查状态
         */
        e2e::ProfileCheckStatus GetProfileCheckStatus() const noexcept {
            return e2e_status_;
        }

    private:
        T* ptr_;                                    // 指向样本数据的原始指针
        std::size_t sample_id_;                     // 样本在内存池中的ID
        void* com_instance_;                        // ara::com实例指针
        e2e::ProfileCheckStatus e2e_status_;       // E2E检查状态
        
        /**
         * @brief 释放样本内存槽的内部接口
         * @param com_instance ara::com实例指针
         * @param sample_id 样本ID
         * 
         * 注意：这是一个占位符实现，在实际产品中需要：
         * 1. 包含适当的ara::com头文件
         * 2. 调用具体的内存管理接口
         * 3. 处理可能的异常情况
         */
        static void ReleaseSampleSlot(void* com_instance, std::size_t sample_id) noexcept {
            // 实际实现示例：
            // auto* instance = static_cast<ara::com::internal::ComInstance*>(com_instance);
            // instance->GetMemoryManager()->ReleaseSampleSlot(sample_id);
            
            // 当前占位符实现 - 避免编译错误
            (void)com_instance;
            (void)sample_id;
        }
    };

    /**
     * @brief 交换两个SamplePtr的内容（非成员函数）
     */
    template<typename T>
    void swap(SamplePtr<T>& lhs, SamplePtr<T>& rhs) noexcept {
        lhs.Swap(rhs);
    }

    /**
     * @brief 比较操作符：相等
     */
    template<typename T>
    bool operator==(const SamplePtr<T>& lhs, const SamplePtr<T>& rhs) noexcept {
        return lhs.get() == rhs.get();
    }

    /**
     * @brief 比较操作符：不相等
     */
    template<typename T>
    bool operator!=(const SamplePtr<T>& lhs, const SamplePtr<T>& rhs) noexcept {
        return !(lhs == rhs);
    }

    /**
     * @brief 与nullptr的比较操作符
     */
    template<typename T>
    bool operator==(const SamplePtr<T>& ptr, std::nullptr_t) noexcept {
        return !ptr;
    }

    template<typename T>
    bool operator==(std::nullptr_t, const SamplePtr<T>& ptr) noexcept {
        return !ptr;
    }

    template<typename T>
    bool operator!=(const SamplePtr<T>& ptr, std::nullptr_t) noexcept {
        return static_cast<bool>(ptr);
    }

    template<typename T>
    bool operator!=(std::nullptr_t, const SamplePtr<T>& ptr) noexcept {
        return static_cast<bool>(ptr);
    }

} // namespace aether::com

#endif // SAMPLE_PTR_HPP