//
// Created by 15531 on 2025/4/15.
//

#include <string>
#include "result.hpp"
#include "error_code.hpp"
#include "log_system_guard.hpp"
#include "event/event_base.hpp"

// 在当前文件初始化logger
#define TAG "ara::com"



aether::core::Result<void> test(int testcode){
    if(testcode == 1){
        //成功
        return{};
    }
    else{
        //失败
        return aether::core::MakeErrorInfo(aether::core::ErrorDomain::Application, 100, "test error");
    }

}

aether::core::Result<std::string> test2(int testcode){
    if(testcode == 1){
        //成功
        return{"success"};
    }
    else{
        //失败
        return aether::core::MakeErrorInfo(aether::core::ErrorDomain::Application, 100, "test error 2");
    }

}


int main()
{
    // 配置并初始化日志系统
    aether::Utils::log::LogConfig config;
    config.level = aether::Utils::log::LogLevel::Debug; // 设置日志级别
    aether::Utils::log::LogSystemGuard logGuard(config);

    LOG_INFO(TAG, "Hello, World!");

    auto resultSuccess = test(2);
    if (!resultSuccess.has_value())
    {
        LOG_INFO(TAG, "即使void,has_value接口仍然有效");
    }

    auto resultSuccess2 = test2(1);
    if (!resultSuccess2)
    {
        LOG_ERROR(TAG, "Domain:{} code:{} message:{}",
            resultSuccess2.error().error_domain,
            resultSuccess2.error().error_code,
            resultSuccess2.error().error_message);
    }
    else{
        LOG_INFO(TAG, "test2 success:{}", resultSuccess2.value());
    }


    return 0;
}
