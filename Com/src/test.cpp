//
// Created by 15531 on 2025/4/15.
//

#include <string>
#include "result.hpp"
#include "error_code.hpp"
#include "log.hpp"


// 在当前文件初始化logger
#define TAG "ara::com"


int main()
{
    // 配置并初始化日志系统
    // ara::utils::log::LogConfig config;
    // config.level = ara::utils::log::LogLevel::Debug; // 设置日志级别
    // ara::utils::log::LogSystemGuard::init(config);
    LOG_INFO(TAG, "Hello, World!");



    return 0;
}
