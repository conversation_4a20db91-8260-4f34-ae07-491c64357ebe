## AUTOSAR Adaptive Platform `ara::com` 客户端事件数据访问详细设计

本文档详细阐述了 AUTOSAR Adaptive Platform (AP) 中 `ara::com` 客户端（Proxy）侧在成功订阅事件后，如何访问接收到的事件数据样本（Sample）的机制。它是对 `EventDetailedDesign.md` 中相关部分的细化和聚焦。

### 1. 访问事件数据 (Sample)

一旦客户端成功订阅了一个事件，服务方发送的事件数据就会开始在通信中间件的缓冲区中累积或排队。客户端应用程序需要一种方式来从这些缓冲区中获取并处理这些数据。

#### 1.1 `GetNewSamples()` 方法

`GetNewSamples()` 是 `ara::com` 事件包装类提供的核心 API，用于触发从中间件缓冲区获取新的事件样本。

*   **功能与参数**:
    *   该方法期望一个**可调用对象 (Callable)** `f` 作为其主要参数。这个可调用对象（例如 lambda 表达式、函数指针或函数对象）将被 `GetNewSamples()` 回调，并且在回调时会接收一个 `ara::com::SamplePtr<SampleType const>` 类型的参数。`SampleType` 是该事件所承载的数据的具体类型。
    *   `GetNewSamples()` 还可以接受一个可选的 `maxNumberOfSamples` 参数，用于指定在单次调用中最多希望处理多少个样本。如果未提供此参数，则会尝试处理所有可用的新样本，直到满足其他停止条件。
*   **返回值**:
    *   `GetNewSamples()` 方法返回一个 `ara::core::Result<size_t>` 类型的值。
        *   如果成功，`Result` 将包含一个 `size_t` 值，表示本次调用中实际获取并呈现给用户（通过回调 `f`）的事件样本数量。
        *   如果发生错误（例如，应用程序持有的样本数超限），`Result` 将包含一个 `ara::core::ErrorCode`，描述了发生的具体错误。
*   **样本数量检查**:
    *   通信管理层 (CM) 会在处理 `GetNewSamples()` 调用时，检查应用程序当前持有的（即尚未被应用程序释放的 `SamplePtr` 指向的）事件样本数量是否已经超过了在 `Subscribe()` 时通过 `maxSampleCount` 参数所承诺的最大数量。如果超过了这个限制，`GetNewSamples()` 将返回一个错误码（通常是 `ara::com::ComErrc::kMaxSamplesReached` 或类似的错误），并且不会再向应用程序提供新的样本，以防止超出预期的内存消耗。
*   **处理循环**:
    *   `GetNewSamples()` 会重复地从缓冲区中检查新的事件样本，并为每个新样本调用用户提供的可调用对象 `f`。这个过程会一直持续，直到以下任一条件满足：
        1.  中间件的事件缓冲区中没有更多新的样本可供处理。
        2.  如果调用时指定了 `maxNumberOfSamples` 参数，并且已经处理了达到该数量的样本。
        3.  应用程序持有的样本数量已达到 `Subscribe()` 时设定的 `maxSampleCount` 上限。
*   **线程安全与重入性**:
    *   `GetNewSamples()` 方法被设计为可重入且线程安全的。然而，与 `Subscribe()` 类似，对同一个 `Event` 类实例进行重入或并发调用 `GetNewSamples()` 时的行为是未定义的。这意味着应避免在多个线程中无同步地对同一个事件实例调用 `GetNewSamples()`。

#### 1.2 `SamplePtr` 的语义

`ara::com::SamplePtr<SampleType const>` 是一个智能指针，用于管理从 `GetNewSamples()` 获取的事件样本的生命周期。它在语义上类似于 `std::unique_ptr`，强调所有权的转移。

*   **所有权转移**: 当 CM 通过回调 `f` 将一个 `SamplePtr` 传递给应用程序时，底层事件样本的所有权就从 CM 转移到了应用程序。应用程序从此负责管理该样本的生命周期。
*   **生命周期管理**: 只要应用程序持有该 `SamplePtr`（即 `SamplePtr` 未被销毁，也未通过移动赋值操作转移所有权），CM 就无法回收该样本所占用的内存槽。当 `SamplePtr` 被销毁（例如，离开其作用域）或其所有权被转移时，它所指向的样本内存才可能被 CM 回收。
*   **内存分配**: 事件样本的内存槽通常由 `ara::com` 的实现（即绑定）在 `Subscribe()` 调用期间根据 `maxSampleCount` 参数进行预分配。这确保了客户端有足够的本地空间来存储一定数量的并发事件样本。
*   **样本处理决策**: 在用户提供的回调函数 `f` 中，应用程序可以访问 `SamplePtr` 指向的事件数据。此时，应用程序可以决定如何处理这个样本：
    *   **保留样本**: 如果需要稍后处理或在回调函数作用域之外访问该样本，应用程序可以将 `SamplePtr` **移动 (move)** 到外部作用域的某个容器或变量中（例如，`std::vector<ara::com::SamplePtr<SampleType const>>`）。
    *   **丢弃样本**: 如果样本在回调函数 `f` 内部处理完毕，并且不再需要，那么当 `SamplePtr` 在回调结束时离开作用域并被销毁后，其管理的内存就会被标记为可回收。
*   **1:N 通信优化**: 在事件通信的 1:N（一个服务提供者对多个客户端）场景中，`SamplePtr` 的设计允许一种优化。客户端的“本地事件缓存”可能并不实际存储事件数据的完整副本，而是存储指向中央 CM 管理的缓冲区中实际数据的指针或引用。这样，当事件发生时，CM 可以通过 `SamplePtr` 将数据的引用传递给多个客户端，从而避免了为每个客户端都进行数据值复制的开销，实现了“参考更新”而非“值复制”。
*   **E2E 检查结果访问**: `SamplePtr` 提供了一个 `GetProfileCheckStatus()` 方法。如果事件配置了端到端（E2E）保护，应用程序可以通过此方法访问该特定样本的 E2E 检查结果（例如，`kOk`, `kRepeated`, `kWrongSequence` 等）。
*   **悬空指针风险**: `SamplePtr` 指向的数据样本的生命周期与分发该 `SamplePtr` 的 `Event`（或 `Field`）对象以及其所属的 `Proxy` 实例的生命周期紧密相关。
    *   如果 `Proxy` 实例或其内部的 `Event`/`Field` 对象被销毁，那么之前从此 `Event`/`Field` 获取的任何 `SamplePtr` 实例都将变成**悬空指针 (dangling pointer)**，访问它们将导致未定义行为。
    *   类似地，如果通过调用 `Unsubscribe()` 方法停止了对事件或字段的活跃订阅，那么之前获取的 `SamplePtr` 也可能会悬空，因为底层的资源可能已被释放。

#### 1.3 `GetFreeSampleCount()` 方法

事件包装类还提供了一个 `GetFreeSampleCount()` 方法，用于查询当前客户端本地缓存中空闲/可用的事件样本槽数量。

*   **返回值**: 该方法返回一个 `size_t` 类型的值，表示在 `Subscribe()` 时根据 `maxSampleCount` 分配的本地缓存中，当前还有多少个未被应用程序持有的（即 `SamplePtr` 已释放的）事件样本数据槽。
*   **用途**: 应用程序可以使用此方法来了解本地缓存的填充情况，例如，在决定是否可以安全地处理更多样本或是否需要调整处理策略时。
*   **线程安全与重入性**: `GetFreeSampleCount()` 方法对于同一 `Event` 类实例以及不同的 `Event` 类实例都是可重入且线程安全的。

### 2. 事件访问模式

`ara::com` 支持两种主要的模式来访问新接收到的事件数据：事件驱动模式和轮询模式。

#### 2.1 事件驱动模式 (Event-Driven)

在事件驱动模式下，应用程序不需要周期性地主动检查是否有新事件到达。相反，当新事件数据可用时，CM 会异步地通知应用程序。

*   **`SetReceiveHandler()` API**: 通过调用事件包装类提供的 `SetReceiveHandler()` 方法，应用程序可以注册一个用户定义的回调函数（接收处理器）。
*   **异步回调**: 一旦注册了接收处理器，每当有新的事件数据到达（特指自上次调用 `GetNewSamples()` 以来新到达的，或者如果从未调用过 `GetNewSamples()` 则是所有新到达的），CM 就会异步地调用这个注册的处理器。
*   **处理器序列化**: 注册的接收处理器不需要是可重入的，因为 CM 会确保对该回调的调用是序列化的。即，对于同一个事件实例，CM 不会并发地调用其注册的接收处理器。
*   **在处理器中调用 `GetNewSamples()`**: `ara::com` 规范明确允许在注册的接收处理器回调函数内部调用 `GetNewSamples()`。这是一种常见的模式：当处理器被通知有新事件时，它立即调用 `GetNewSamples()` 来获取并处理这些新事件。
*   **执行上下文**: 与 `SetSubscriptionStateChangeHandler` 类似，`SetReceiveHandler()` 也提供了带有可选执行上下文参数的重载版本，允许应用程序对接收处理器的执行环境进行控制。
*   **`UnsetReceiveHandler()` API**: 如果不再需要事件驱动的通知，可以通过调用 `UnsetReceiveHandler()` 来注销之前注册的接收处理器。
*   **线程安全与重入性**: `SetReceiveHandler()` 和 `UnsetReceiveHandler()` 方法对于不同的 `Event` 类实例是可重入且线程安全的。但是，对同一个 `Event` 类实例进行重入或并发调用这些方法时的行为是未定义的。
*   **上下文切换**: 当注册了 `SetReceiveHandler()` 后，新事件的接收通常会导致一个隐式的上下文切换，以便调度和调用注册的 `ReceiveHandler`。这意味着处理器的执行可能不在应用程序的主动调用流程中。

#### 2.2 轮询模式 (Polling)

在轮询模式下，应用程序通过周期性地、显式地调用 `GetNewSamples()` 方法来检查是否有新的事件数据到达。

*   **无接收处理器**: 在此模式下，应用程序不注册任何接收处理器（即不调用 `SetReceiveHandler()`，或者调用了 `UnsetReceiveHandler()`）。
*   **显式获取**: 新的事件样本数据只能通过直接调用 `GetNewSamples()` 来接收和处理。
*   **无隐式上下文切换**: 与事件驱动模式不同，事件的接收本身（即数据到达中间件缓冲区）在轮询模式下通常不会导致客户端接收进程中的隐式上下文切换。上下文切换只发生在应用程序主动调用 `GetNewSamples()` 并处理数据时。

选择事件驱动模式还是轮询模式取决于应用程序的具体需求，例如对实时性的要求、处理事件的逻辑复杂度以及系统资源的考虑等。