## AUTOSAR Adaptive Platform `ara::com` 服务端订阅监控与E2E保护详细设计

本文档详细阐述了 AUTOSAR Adaptive Platform (AP) 中 `ara::com` 服务端（Skeleton）侧如何监控客户端对其事件的订阅状态，以及在发送事件时如何应用端到端（End-to-End, E2E）通信保护机制。它是对 `EventDetailedDesign.md` 中相关部分的细化和聚焦。

### 1. 监控骨架侧的订阅状态

服务端（骨架侧）也可能需要了解其提供的事件是否有客户端正在订阅，这对于资源管理或按需生成事件数据等场景可能非常有用。`ara::com` 为此提供了相应的机制。

#### 1.1 `GetSubscriptionState()` 方法

与客户端类似，骨架侧的事件包装器类（例如 `skeleton::events::MyEvent`）也提供了一个 `GetSubscriptionState()` 方法，用于查询当前事件的总体订阅状态。

*   **返回值与语义**: 在骨架侧，`GetSubscriptionState()` 方法返回的 `ara::com::SubscriptionState` 枚举值的语义有所不同：
    *   `ara::com::SubscriptionState::kSubscribed`: 表示当前**至少有一个**客户端活跃地订阅了该事件。
    *   `ara::com::SubscriptionState::kNotSubscribed`: 表示当前**没有任何**客户端订阅该事件。
    *   `ara::com::SubscriptionState::kSubscriptionPending`: 这个状态在服务器侧**不应被使用或返回**。因为服务器是被动接受订阅请求的一方，它只关心最终是否有有效的订阅。

#### 1.2 `SetSubscriptionStateChangeHandler()` 方法

骨架侧同样提供了 `SetSubscriptionStateChangeHandler()` API，允许服务实现注册一个回调处理器，以便在事件的总体订阅状态发生变化时得到通知。

*   **处理器调用条件**:
    *   当活跃订阅该事件的客户端数量从 **0 变为大于 0** 时（即第一个客户端成功订阅），注册的处理器将被调用，并传入 `ara::com::SubscriptionState::kSubscribed` 状态。
    *   当活跃订阅该事件的客户端数量从 **大于 0 变为 0** 时（即最后一个客户端取消订阅或断开连接），注册的处理器将被调用，并传入 `ara::com::SubscriptionState::kNotSubscribed` 状态。
*   **用途**: 服务实现可以利用这个回调来动态调整行为。例如：
    *   当没有客户端订阅时，可以停止生成或计算相应的事件数据，以节省资源。
    *   当有客户端开始订阅时，再开始生成并发送事件数据。
*   **执行上下文**: 与客户端类似，骨架侧的 `SetSubscriptionStateChangeHandler` 也可能提供带有可选执行上下文参数的重载，允许对处理器的执行环境进行控制。
*   **`UnsetSubscriptionStateChangeHandler()`**: 用于注销之前注册的状态变化处理器。

#### 1.3 线程安全与重入性

`GetSubscriptionState()`、`SetSubscriptionStateChangeHandler()` 和 `UnsetSubscriptionStateChangeHandler()` 这些方法，当它们作用于**不同**的 `Event` 类实例（即服务提供的不同事件）时，通常是可重入且线程安全的。

然而，对于**同一个** `Event` 类实例，对其进行重入或并发调用这些方法时的行为是**未定义**的。服务实现需要确保对同一个事件实例的这些操作进行适当的同步。

### 2. 端到端 (E2E) 通信保护 (发布方/服务端侧)

对于配置了端到端 (E2E) 保护的事件，服务端在发送这些事件时，需要在将数据交给底层通信绑定之前，先执行 E2E 保护步骤（例如，计算并附加校验和、序列号等）。这个过程通常在 `Send()` 方法的调用上下文中执行。

#### 2.1 E2E 保护流程

1.  **数据准备**: 服务实现准备好要发送的原始事件数据 `SampleType`。
2.  **调用 `Send()`**: 服务实现调用骨架事件包装器提供的 `Send()` 方法（无论是接受 `const SampleType&` 的版本还是接受 `SampleAllocateePtr<SampleType>` 的版本）。
3.  **序列化**: 在 `Send()` 方法的内部实现中（通常由 `ara::com` 绑定库提供），原始的事件数据样本首先会被**序列化**。序列化的规则取决于所使用的网络绑定协议（例如，SOME/IP 有其特定的序列化规则，DDS 也有其规则）。序列化将应用程序级别的数据结构转换为一段字节流。
4.  **添加协议头**: 序列化后的数据会根据网络绑定的规则被封装。例如，在 SOME/IP 中，会添加 SOME/IP 消息头，其中包含服务 ID、方法 ID（对于事件，通常是事件 ID 加上一个固定的偏移量，如 0x8000）、消息类型（例如 `NOTIFICATION`）、返回码（例如 `E_OK`）等。
5.  **E2E 保护头添加 (E2E Protection)**: 在将完整的消息（包含协议头和序列化载荷）发送到网络之前，如果该事件配置了 E2E 保护，那么 E2E 保护相关的头部信息（例如，CRC 校验和、序列计数器等，具体取决于所选的 E2E Profile）将被计算并附加到消息中，或者嵌入到消息的特定字段中。这个步骤由 E2E 库执行，该库会维护发送方 E2E 状态机的状态（例如，递增序列号）。
6.  **发送到网络**: 经过 E2E 保护处理后的最终消息数据包随后被交给底层传输层（如 UDP 或 TCP）进行发送。

#### 2.2 E2E 配置

事件是否需要 E2E 保护，以及使用哪个 E2E Profile（定义了保护算法、序列号长度、CRC 类型等参数），通常是在 ARXML 模型中进行配置的，并通过代码生成工具反映到生成的骨架代码和绑定实现中。

#### 2.3 服务端责任

服务端的主要责任是确保在 `Send()` 被调用时，E2E 保护逻辑能够正确执行。E2E 库会处理大部分复杂的细节，如状态管理和校验码计算。服务开发者通常不需要直接操作 E2E 保护的底层细节，而是依赖于 `ara::com` 框架和配置来确保其正确应用。

通过这些机制，服务端不仅可以有效地管理事件的发送，还可以根据客户端的订阅情况优化资源使用，并为需要高可靠性和完整性的事件数据提供端到端保护。