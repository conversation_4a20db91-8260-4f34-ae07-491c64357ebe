@startuml
skinparam handwritten true
skinparam monochrome true
skinparam packageStyle rectangle
skinparam defaultFontName "Consolas"
skinparam defaultFontSize 12

title AUTOSAR ara::com Event Subscription Flow

actor "应用程序 (Client)" as App
participant "ara::com Proxy (客户端)" as ComProxy
participant "通信管理 (CM) 绑定" as CMBinding
participant "服务提供者 (服务端)" as ServiceProvider

box "客户端 ECU" #LightBlue
    App -> ComProxy : **1. 调用 Subscribe(maxSampleCount)** [3-5]
    activate ComProxy
    note right
        maxSampleCount: 应用程序最多持有的样本数。
        用于设置本地缓存大小。 [4]
    end note

    ComProxy -> ComProxy : 2. 内部验证 (如: maxSampleCount冲突, 返回 ComErrc::kMaxSampleCountNotRealizable) [5]
    alt 本地访问控制检查失败 [6, 7]
        ComProxy --> App : 返回 ComErrc::kGrantEnforcementError [7]
        deactivate ComProxy
        destroy ComProxy
        return Error
    else 本地访问控制检查通过
        ComProxy -> ComProxy : 3. 分配/设置本地事件样本缓存 (maxSampleCount) [4, 8]
        ComProxy --> App : **4. 立即返回 ara::core::Result<void>** (异步操作) [3, 9]
        deactivate ComProxy
    end

    App -> ComProxy : (可选) **5a. 设置 SubscriptionStateChangeHandler()** [23, 30, 31]
    activate ComProxy
    ComProxy -> ComProxy : 注册订阅状态变更处理函数 [23, 30]
    deactivate ComProxy

    App -> ComProxy : (可选, 轮询) **5b. 调用 GetSubscriptionState()** [23-26]
    activate ComProxy
    ComProxy --> App : 返回 SubscriptionState (kSubscriptionPending, kSubscribed, kNotSubscribed) [25, 27-29]
    deactivate ComProxy
end box

box "通信网络 / 中间件 (DDS/SOME/IP)" #LightGreen
    activate CMBinding
    CMBinding -> ServiceProvider : **6. 发送订阅请求** (例如: SOME/IP SubscribeEventgroup [13], DDS DataReader创建 [15])
    note left
        DDS: 创建 DataReader，配置 QoS (如 HISTORY, DURABILITY, RELIABILITY)。 [15, 17]
        DataReaderListener 的 on_subscription_matched() 负责状态更新。 [16]
    end note
end box

box "服务端 ECU" #LightBlue
    activate ServiceProvider
    ServiceProvider -> ServiceProvider : **7. 处理订阅请求** (内部建立订阅)
    alt 远程访问控制检查失败 [18-20]
        ServiceProvider --> CMBinding : 拒绝订阅
        deactivate ServiceProvider
        note right
            CMBinding可能会通过handler通知Proxy错误。
        end note
        return Denial
    else 远程访问控制检查通过
        ServiceProvider -> CMBinding : **8. 发送订阅确认** (例如: SOME/IP SubscribeEventgroupAck [21, 22])
        deactivate ServiceProvider
    end
end box

box "客户端 ECU" #LightBlue
    activate CMBinding
    CMBinding -> ComProxy : **9. 通知 ComProxy 订阅状态变更**
    activate ComProxy
    ComProxy -> App : (若已注册) **10a. 调用 SubscriptionStateChangeHandler(kSubscriptionPending)** [33, 34]
    note right
        此回调可能在 Subscribe() 返回前发生 (异步) [10]。
    end note
    ComProxy -> App : (收到确认后) **10b. 调用 SubscriptionStateChangeHandler(kSubscribed)** [34, 35]
    deactivate ComProxy
    deactivate CMBinding
end box

box "数据流" #LightBlue
    ServiceProvider -> CMBinding : **11. 发送事件样本** (订阅激活后) [37]
    activate CMBinding
    CMBinding -> ComProxy : **12. 接收事件样本** (存储到本地缓存，数量受 maxSampleCount 限制)
    activate ComProxy
    App -> ComProxy : (可选) **13a. 调用 SetReceiveHandler()** (注册接收回调) [38, 40]
    activate ComProxy
    ComProxy -> App : (若已注册) **14a. 调用 EventReceiveHandler(SamplePtr)** (新样本到来) [41]
    deactivate ComProxy
    deactivate ComProxy
    deactivate CMBinding

    App -> ComProxy : (轮询) **13b. 调用 GetNewSamples()** [38, 39]
    activate ComProxy
    ComProxy --> App : **14b. 返回 SamplePtr(s)** (可用的新样本) [39]
    deactivate ComProxy
end box

box "客户端 ECU" #LightBlue
    App -> ComProxy : **15. 调用 Unsubscribe()** (停止订阅) [24, 26, 43]
    activate ComProxy
    ComProxy -> CMBinding : 发送取消订阅请求
    activate CMBinding
    CMBinding -> CMBinding : 删除 DataReader (DDS) [46, 47]
    deactivate CMBinding
    note right
        已持有的 SamplePtr 将变为悬空。 [45]
    end note
    deactivate ComProxy
end box
@enduml