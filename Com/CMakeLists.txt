cmake_minimum_required(VERSION 3.28)
list(APPEND CMAKE_PREFIX_PATH ~/Fast-DDS/install)
# --- Find required system libraries
find_package(Threads REQUIRED)
find_package(fastcdr 2 REQUIRED)
find_package(fastdds 3 REQUIRED)


include_directories(inc)

add_library(aether_com SHARED)
target_sources(aether_com PRIVATE
        ${CMAKE_CURRENT_LIST_DIR}/src/test.cpp
        # 新的静多态架构头文件（仅用于IDE支持，不参与编译）
        # ${CMAKE_CURRENT_LIST_DIR}/inc/event/event_interface.hpp
        # ${CMAKE_CURRENT_LIST_DIR}/inc/event/dds_event.hpp
        # ${CMAKE_CURRENT_LIST_DIR}/inc/event/someip_event.hpp
        # ${CMAKE_CURRENT_LIST_DIR}/inc/event/event_factory.hpp
)


target_link_libraries(aether_com PUBLIC
        fastdds
        fastcdr
)
# internal lib
target_link_libraries(aether_com PUBLIC
        aether_utils
        aether_core
)



target_include_directories(aether_com PUBLIC
        ${CMAKE_CURRENT_LIST_DIR}/inc
)

add_executable(comtest 
        ${CMAKE_CURRENT_LIST_DIR}/src/test.cpp
)
target_link_libraries(comtest 
        aether_com
)


