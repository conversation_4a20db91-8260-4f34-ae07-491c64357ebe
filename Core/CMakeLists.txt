cmake_minimum_required(VERSION 3.28)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Performance optimization flags
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG -flto")
endif()

# Create main library as header-only (all implementations are now in headers)
add_library(aether_core INTERFACE)

target_sources(aether_core INTERFACE
    FILE_SET HEADERS
    BASE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}
    FILES
        instance_Specifier.hpp
        error_code.hpp
        error_domain.hpp
        error_types.hpp
        result.hpp
)

target_include_directories(aether_core INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include/ara/core>
)

# Compiler-specific optimizations for interface library
target_compile_options(aether_core INTERFACE
    $<$<CXX_COMPILER_ID:GNU>:-Wall -Wextra -Wpedantic -Wno-unused-parameter>
    $<$<CXX_COMPILER_ID:Clang>:-Wall -Wextra -Wpedantic -Wno-unused-parameter>
    $<$<CXX_COMPILER_ID:MSVC>:/W4>
)

target_compile_features(aether_core INTERFACE cxx_std_17)

# Enable Link Time Optimization for Release builds
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set_property(TARGET aether_core PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
endif()





add_executable(result_benchmark
    benchmark/result_benchmark.cpp
)

target_link_libraries(result_benchmark PRIVATE aether_core)


# Correct Err usage example
add_executable(correct_err_usage
    example/correct_err_usage.cpp
)

target_link_libraries(correct_err_usage PRIVATE aether_core)



# Production quality benchmark
add_executable(result_production_benchmark
    benchmark/result_production_benchmark.cpp
)

target_link_libraries(result_production_benchmark PRIVATE aether_core)

# Testing configuration
option(BUILD_TESTS "Build unit tests" OFF)

if(BUILD_TESTS)
    # Find Google Test
    find_package(GTest QUIET)

    if(NOT GTest_FOUND)
        # Download and build Google Test if not found
        include(FetchContent)
        FetchContent_Declare(
            googletest
            URL https://github.com/google/googletest/archive/03597a01ee50f33f9142fd2db563d69b413611e6.zip
        )

        # For Windows: Prevent overriding the parent project's compiler/linker settings
        set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
        FetchContent_MakeAvailable(googletest)
    endif()

    # Create test executables
    add_executable(test_err
        test/test_err.cpp
    )

    add_executable(test_result_production_quality
        test/test_result_production_quality.cpp
    )

    target_link_libraries(test_err PRIVATE aether_core gtest_main gtest)
    target_link_libraries(test_result_production_quality PRIVATE aether_core gtest_main gtest)

    # Enable testing
    enable_testing()

    # Add tests
    add_test(NAME err_test COMMAND test_err)
    add_test(NAME result_production_quality_test COMMAND test_result_production_quality)
endif()

# Installation
install(TARGETS aether_core
    EXPORT aether_core_targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    FILE_SET HEADERS DESTINATION include/ara/core
)

install(EXPORT aether_core_targets
    FILE aether_core_targets.cmake
    NAMESPACE aether::
    DESTINATION lib/cmake/aether_core
)

# Create package config file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    aether_core_config_version.cmake
    VERSION 1.0.0
    COMPATIBILITY SameMajorVersion
)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/aether_core_config.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/aether_core_config.cmake
    INSTALL_DESTINATION lib/cmake/aether_core
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/aether_core_config.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/aether_core_config_version.cmake
    DESTINATION lib/cmake/aether_core
)
