#include "../result.hpp"
#include "../error_code.hpp"
#include "../error_domain.hpp"
#include "../error_types.hpp"
#include <chrono>
#include <iostream>

using namespace ara::core;

int main() {
    std::cout << "Result 生产级基准测试" << std::endl;
    
    // 基本性能测试
    const int iterations = 1000000;
    
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < iterations; ++i) {
        volatile auto result = Ok(42);
        (void)result;
    }
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    std::cout << "构造性能: " << duration.count() << " ns for " << iterations << " iterations" << std::endl;
    std::cout << "平均每次: " << duration.count() / iterations << " ns" << std::endl;
    
    return 0;
}
