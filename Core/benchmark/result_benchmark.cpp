#include "../result.hpp"
#include "../error_code.hpp"
#include "../error_domain.hpp"
#include "../error_types.hpp"
#include <chrono>
#include <iostream>
#include <vector>
#include <string>
#include <string_view>
#include <random>
#include <optional>
#include <memory>
#include <iomanip>
#include <stdexcept>
#include <new>

using namespace ara::core;

// 测试用错误枚举
enum class BenchmarkError : ErrorCodeType {
    SUCCESS = 0,
    INVALID_INPUT = 1,
    TIMEOUT = 2,
    NETWORK_ERROR = 3,
    MEMORY_ERROR = 4
};

// 测试用错误域
class BenchmarkErrorDomain final : public ErrorDomain {
public:
    constexpr BenchmarkErrorDomain() noexcept : ErrorDomain(0xC000123400000010) {}

    std::string_view Name() const noexcept override {
        return "BenchmarkErrorDomain";
    }

    std::string_view Message(CodeType errorCode) const noexcept override {
        switch (static_cast<BenchmarkError>(errorCode)) {
            case BenchmarkError::SUCCESS: return "Success";
            case BenchmarkError::INVALID_INPUT: return "Invalid input";
            case BenchmarkError::TIMEOUT: return "Timeout";
            case BenchmarkError::NETWORK_ERROR: return "Network error";
            case BenchmarkError::MEMORY_ERROR: return "Memory error";
            default: return "Unknown error";
        }
    }

    void ThrowAsException(const ErrorCode& errorCode) const override {
        throw std::runtime_error(std::string(Message(errorCode.Value())));
    }
};

inline constexpr BenchmarkErrorDomain g_benchmark_domain{};

constexpr ErrorCode MakeErrorCode(BenchmarkError e, ErrorSupportDataType data = ErrorSupportDataType()) noexcept {
    return ErrorCode(static_cast<ErrorCodeType>(e), g_benchmark_domain, data);
}

// 高精度基准测试类
class PrecisionBenchmark {
private:
    std::string name_;
    std::chrono::high_resolution_clock::time_point start_;
    
public:
    explicit PrecisionBenchmark(const std::string& name) : name_(name) {
        start_ = std::chrono::high_resolution_clock::now();
    }
    
    ~PrecisionBenchmark() {
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start_);
        std::cout << name_ << ": " << duration.count() << " ns (" 
                  << std::fixed << std::setprecision(2) 
                  << duration.count() / 1000000.0 << " ms)" << std::endl;
    }
};

#define BENCHMARK(name) PrecisionBenchmark _bench(name)

// 测试函数
Result<int> safe_divide(int a, int b) {
    if (b == 0) {
        return Err(BenchmarkError::INVALID_INPUT);
    }
    return Ok(a / b);
}

Result<std::string> process_string(const std::string& input) {
    if (input.empty()) {
        return Err(BenchmarkError::INVALID_INPUT);
    }
    if (input.length() > 1000) {
        return Err(BenchmarkError::TIMEOUT);
    }
    return Ok(input + "_processed");
}

Result<std::unique_ptr<int>> allocate_int(int value) {
    try {
        return Ok(std::make_unique<int>(value));
    } catch (const std::bad_alloc&) {
        return Err(BenchmarkError::MEMORY_ERROR);
    }
}

// 与 std::optional 的性能对比
std::optional<int> optional_divide(int a, int b) {
    if (b == 0) {
        return std::nullopt;
    }
    return a / b;
}

// 异常版本对比
int exception_divide(int a, int b) {
    if (b == 0) {
        throw std::runtime_error("Division by zero");
    }
    return a / b;
}

void benchmark_construction_performance() {
    std::cout << "\n=== 构造性能基准测试 ===" << std::endl;
    
    const int iterations = 1000000;
    
    // Result<int> 成功构造
    {
        BENCHMARK("Result<int> Ok construction (1M iterations)");
        for (int i = 0; i < iterations; ++i) {
            volatile auto result = Ok(42);
            (void)result;
        }
    }
    
    // Result<int> 错误构造
    {
        BENCHMARK("Result<int> Err construction (1M iterations)");
        for (int i = 0; i < iterations; ++i) {
            volatile auto result = Err(BenchmarkError::INVALID_INPUT);
            (void)result;
        }
    }
    
    // std::optional 对比
    {
        BENCHMARK("std::optional<int> construction (1M iterations)");
        for (int i = 0; i < iterations; ++i) {
            volatile auto opt = std::optional<int>(42);
            (void)opt;
        }
    }
    
    // std::optional nullopt 对比
    {
        BENCHMARK("std::optional<int> nullopt construction (1M iterations)");
        for (int i = 0; i < iterations; ++i) {
            volatile auto opt = std::optional<int>(std::nullopt);
            (void)opt;
        }
    }
}

void benchmark_access_performance() {
    std::cout << "\n=== 访问性能基准测试 ===" << std::endl;
    
    const int iterations = 10000000;  // 更多迭代以测试访问性能
    
    // Result 成功访问
    {
        BENCHMARK("Result<int> unwrap access (10M iterations)");
        Result<int> result = Ok(42);
        for (int i = 0; i < iterations; ++i) {
            volatile int value = result.unwrap();
            (void)value;
        }
    }

    // Result 状态检查
    {
        BENCHMARK("Result<int> is_ok check (10M iterations)");
        Result<int> result = Ok(42);
        for (int i = 0; i < iterations; ++i) {
            volatile bool ok = result.is_ok();
            (void)ok;
        }
    }
    
    // std::optional 对比
    {
        BENCHMARK("std::optional<int> value access (10M iterations)");
        auto opt = std::optional<int>(42);
        for (int i = 0; i < iterations; ++i) {
            volatile int value = opt.value();
            (void)value;
        }
    }
    
    // std::optional 状态检查
    {
        BENCHMARK("std::optional<int> has_value check (10M iterations)");
        auto opt = std::optional<int>(42);
        for (int i = 0; i < iterations; ++i) {
            volatile bool has = opt.has_value();
            (void)has;
        }
    }
}

void benchmark_chaining_performance() {
    std::cout << "\n=== 链式操作性能基准测试 ===" << std::endl;
    
    const int iterations = 100000;
    
    // Result 链式操作 - 成功路径
    {
        BENCHMARK("Result chaining operations - success path (100K iterations)");
        for (int i = 0; i < iterations; ++i) {
            auto result = safe_divide(100, 2)
                .map([](int x) { return x * 2; })
                .and_then([](int x) { return safe_divide(x, 4); })
                .map([](int x) { return x + 10; });
            volatile bool ok = result.is_ok();
            (void)ok;
        }
    }
    
    // Result 链式操作 - 错误路径
    {
        BENCHMARK("Result chaining operations - error path (100K iterations)");
        for (int i = 0; i < iterations; ++i) {
            auto result = safe_divide(100, 0)  // 这会产生错误
                .map([](int x) { return x * 2; })
                .and_then([](int x) { return safe_divide(x, 4); })
                .map([](int x) { return x + 10; });
            volatile bool ok = result.is_ok();
            (void)ok;
        }
    }
}

void benchmark_memory_usage() {
    std::cout << "\n=== 内存使用基准测试 ===" << std::endl;
    
    std::cout << "sizeof(Result<int>): " << sizeof(Result<int>) << " bytes" << std::endl;
    std::cout << "sizeof(std::optional<int>): " << sizeof(std::optional<int>) << " bytes" << std::endl;
    std::cout << "sizeof(Result<std::string>): " << sizeof(Result<std::string>) << " bytes" << std::endl;
    std::cout << "sizeof(std::optional<std::string>): " << sizeof(std::optional<std::string>) << " bytes" << std::endl;
    std::cout << "sizeof(Result<void>): " << sizeof(Result<void>) << " bytes" << std::endl;
    std::cout << "sizeof(ErrorCode): " << sizeof(ErrorCode) << " bytes" << std::endl;
}

int main() {
    std::cout << "Result<T, E> 生产级性能基准测试" << std::endl;
    std::cout << "=================================" << std::endl;
    
    benchmark_memory_usage();
    benchmark_construction_performance();
    benchmark_access_performance();
    benchmark_chaining_performance();
    
    std::cout << "\n=== 基准测试完成 ===" << std::endl;
    std::cout << "注意：较低的纳秒数表示更好的性能" << std::endl;
    
    return 0;
}
