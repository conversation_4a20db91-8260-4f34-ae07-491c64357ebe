/**
 * Result<T,E> 生产级质量完整使用示例
 *
 * 本示例展示了 Result<T,E> 在实际项目中的最佳实践，包括：
 * - 完整的错误域定义和业务错误处理
 * - 基本操作和安全访问模式
 * - 函数式链式操作和错误传播
 * - void Result 处理和错误恢复策略
 * - 性能特性和 constexpr 支持
 * - 与 ErrorCode 的完整集成
 */

#include "../result.hpp"
#include "../error_code.hpp"
#include "../error_domain.hpp"
#include "../error_types.hpp"
#include <iostream>
#include <string>
#include <vector>
#include <string_view>
#include <stdexcept>
#include <exception>
#include <memory>
#include <fstream>
#include <chrono>
#include <thread>

using namespace ara::core;

// ============================================================================
// 1. 完整的错误域定义 - 模拟文件系统错误
// ============================================================================

enum class FileSystemError : ErrorCodeType {
    SUCCESS = 0,
    FILE_NOT_FOUND = 1,
    PERMISSION_DENIED = 2,
    DISK_FULL = 3,
    INVALID_PATH = 4,
    FILE_ALREADY_EXISTS = 5,
    IO_ERROR = 6
};

class FileSystemErrorDomain final : public ErrorDomain {
public:
    constexpr FileSystemErrorDomain() noexcept : ErrorDomain(0xC000123400000001) {}

    std::string_view Name() const noexcept override {
        return "FileSystemErrorDomain";
    }

    std::string_view Message(CodeType errorCode) const noexcept override {
        switch (static_cast<FileSystemError>(errorCode)) {
            case FileSystemError::SUCCESS: return "Success";
            case FileSystemError::FILE_NOT_FOUND: return "File not found";
            case FileSystemError::PERMISSION_DENIED: return "Permission denied";
            case FileSystemError::DISK_FULL: return "Disk full";
            case FileSystemError::INVALID_PATH: return "Invalid file path";
            case FileSystemError::FILE_ALREADY_EXISTS: return "File already exists";
            case FileSystemError::IO_ERROR: return "I/O error";
            default: return "Unknown file system error";
        }
    }

    void ThrowAsException(const ErrorCode& errorCode) const override {
        throw std::runtime_error(std::string(Message(errorCode.Value())));
    }
};

inline constexpr FileSystemErrorDomain g_filesystem_domain{};

// ADL 函数 - 自动错误码转换
constexpr ErrorCode MakeErrorCode(FileSystemError e, ErrorSupportDataType data = ErrorSupportDataType()) noexcept {
    return ErrorCode(static_cast<ErrorCodeType>(e), g_filesystem_domain, data);
}

// ============================================================================
// 2. 网络错误域定义
// ============================================================================

enum class NetworkError : ErrorCodeType {
    SUCCESS = 0,
    CONNECTION_FAILED = 1,
    TIMEOUT = 2,
    DNS_RESOLUTION_FAILED = 3,
    SSL_ERROR = 4,
    HTTP_ERROR = 5,
    INVALID_URL = 6
};

class NetworkErrorDomain final : public ErrorDomain {
public:
    constexpr NetworkErrorDomain() noexcept : ErrorDomain(0xC000123400000002) {}

    std::string_view Name() const noexcept override {
        return "NetworkErrorDomain";
    }

    std::string_view Message(CodeType errorCode) const noexcept override {
        switch (static_cast<NetworkError>(errorCode)) {
            case NetworkError::SUCCESS: return "Success";
            case NetworkError::CONNECTION_FAILED: return "Connection failed";
            case NetworkError::TIMEOUT: return "Network timeout";
            case NetworkError::DNS_RESOLUTION_FAILED: return "DNS resolution failed";
            case NetworkError::SSL_ERROR: return "SSL/TLS error";
            case NetworkError::HTTP_ERROR: return "HTTP error";
            case NetworkError::INVALID_URL: return "Invalid URL";
            default: return "Unknown network error";
        }
    }

    void ThrowAsException(const ErrorCode& errorCode) const override {
        throw std::runtime_error(std::string(Message(errorCode.Value())));
    }
};

inline constexpr NetworkErrorDomain g_network_domain{};

constexpr ErrorCode MakeErrorCode(NetworkError e, ErrorSupportDataType data = ErrorSupportDataType()) noexcept {
    return ErrorCode(static_cast<ErrorCodeType>(e), g_network_domain, data);
}

// ============================================================================
// 3. 业务逻辑层 - 文件操作模拟
// ============================================================================

// 文件信息结构
struct FileInfo {
    std::string name;
    size_t size;
    bool is_directory;

    FileInfo(std::string n, size_t s, bool dir = false)
        : name(std::move(n)), size(s), is_directory(dir) {}
};

// 基本文件操作 - 展示基本的 Ok/Err 构造
Result<FileInfo> getFileInfo(const std::string& path) {
    if (path.empty()) {
        return Err(FileSystemError::INVALID_PATH);
    }
    if (path == "/nonexistent") {
        return Err(FileSystemError::FILE_NOT_FOUND);
    }
    if (path == "/protected") {
        return Err(FileSystemError::PERMISSION_DENIED);
    }

    // 模拟成功获取文件信息
    return Ok(FileInfo(path, 1024));
}

// 文件读取操作
Result<std::string> readFile(const std::string& path) {
    // 首先获取文件信息
    auto info_result = getFileInfo(path);
    if (info_result.is_err()) {
        // 错误传播 - 直接返回错误
        return Err(info_result.unwrap_err());
    }

    auto info = info_result.unwrap();
    if (info.is_directory) {
        return Err(FileSystemError::INVALID_PATH);
    }

    // 模拟读取文件内容
    return Ok("Content of " + path);
}

// void Result 示例 - 文件写入操作
Result<void> writeFile(const std::string& path, const std::string& content) {
    if (path.empty() || content.empty()) {
        return Err(FileSystemError::INVALID_PATH);
    }
    if (path == "/readonly") {
        return Err(FileSystemError::PERMISSION_DENIED);
    }
    if (content.length() > 10000) {
        return Err(FileSystemError::DISK_FULL);
    }

    // 模拟成功写入
    return Ok();
}




// ============================================================================
// 4. 网络操作层 - 展示跨域错误处理
// ============================================================================

// HTTP 响应结构
struct HttpResponse {
    int status_code;
    std::string body;

    HttpResponse(int code, std::string b) : status_code(code), body(std::move(b)) {}
};

// 网络请求操作
Result<HttpResponse> httpGet(const std::string& url) {
    if (url.empty()) {
        return Err(NetworkError::INVALID_URL);
    }
    if (url.find("timeout") != std::string::npos) {
        return Err(NetworkError::TIMEOUT);
    }
    if (url.find("dns") != std::string::npos) {
        return Err(NetworkError::DNS_RESOLUTION_FAILED);
    }
    if (url.find("ssl") != std::string::npos) {
        return Err(NetworkError::SSL_ERROR);
    }

    // 模拟成功的 HTTP 响应
    return Ok(HttpResponse(200, "Response from: " + url));
}

// 数据缓存操作 - void Result 示例
Result<void> cacheResponse(const HttpResponse& response) {
    if (response.body.empty()) {
        return Err(FileSystemError::INVALID_PATH);
    }
    if (response.body.length() > 5000) {
        return Err(FileSystemError::DISK_FULL);
    }

    // 模拟缓存成功
    return Ok();
}

// ============================================================================
// 5. 复杂业务逻辑 - 展示链式操作和错误传播
// ============================================================================

// 数据处理管道 - 展示函数式链式操作
Result<std::string> processWebData(const std::string& url) {
    return httpGet(url)
        .map([](const HttpResponse& response) {
            // 转换 HTTP 响应为字符串
            return response.body + " [processed]";
        })
        .and_then([](const std::string& processed_data) {
            // 尝试缓存处理后的数据
            return cacheResponse(HttpResponse(200, processed_data))
                .map([&processed_data]() {
                    return processed_data;
                });
        });
}

// 文件备份操作 - 展示错误恢复策略
Result<void> backupFile(const std::string& source_path, const std::string& backup_path) {
    return readFile(source_path)
        .and_then([&backup_path](const std::string& content) {
            return writeFile(backup_path, content);
        });
}

// 批量文件处理 - 展示错误传播
Result<std::vector<std::string>> processFiles(const std::vector<std::string>& file_paths) {
    std::vector<std::string> results;
    results.reserve(file_paths.size());

    for (const auto& path : file_paths) {
        auto content_result = readFile(path);
        if (content_result.is_err()) {
            // 遇到错误立即返回，展示错误传播
            return Err(content_result.unwrap_err());
        }
        results.push_back(content_result.unwrap());
    }

    return Ok(std::move(results));
}

// 安全的数学运算 - 运行时版本
Result<int> safe_divide(int a, int b) {
    if (b == 0) {
        return Err(FileSystemError::INVALID_PATH);  // 重用错误类型作为示例
    }
    return Ok(a / b);
}

// 数学计算示例
Result<int> math_calculation() {
    return safe_divide(100, 5)
        .map([](int result) { return result * 2; });
}

// ============================================================================
// 6. 主函数 - 完整示例演示
// ============================================================================

int main() {
    std::cout << "Result<T,E> 生产级质量完整使用示例\n";
    std::cout << "=====================================\n";
    std::cout << "展示 Result<T,E> 在实际项目中的最佳实践\n";

    // 1. 基本操作演示
    std::cout << "\n=== 1. 基本操作演示 ===\n";

    // 成功案例
    Result<FileInfo> file_info = getFileInfo("/home/<USER>/document.txt");
    if (file_info.is_ok()) {
        auto info = file_info.unwrap();
        std::cout << "✅ 文件信息: " << info.name << " (大小: " << info.size << " 字节)\n";
    }

    // 错误案例
    Result<FileInfo> missing_file = getFileInfo("/nonexistent");
    if (missing_file.is_err()) {
        std::cout << "❌ 文件错误: " << missing_file.unwrap_err().Message() << "\n";
    }

    // 2. 安全访问模式演示
    std::cout << "\n=== 2. 安全访问模式演示 ===\n";

    // unwrap_or - 提供默认值
    auto content_with_default = readFile("/nonexistent").unwrap_or("默认内容");
    std::cout << "使用默认值: " << content_with_default << "\n";

    // unwrap_or_else - 使用函数计算默认值
    auto computed_default = readFile("/protected").unwrap_or_else([](const ErrorCode& error) {
        return "错误恢复内容: " + std::string(error.Message());
    });
    std::cout << "计算默认值: " << computed_default << "\n";

    // expect - 带自定义错误消息
    try {
        auto critical_file = getFileInfo("/critical.txt");
        auto info = critical_file.expect("关键文件必须存在");
        std::cout << "✅ 关键文件: " << info.name << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ 期望失败: " << e.what() << "\n";
    }

    // 3. 网络操作演示
    std::cout << "\n=== 3. 网络操作演示 ===\n";

    std::vector<std::string> test_urls = {
        "https://api.example.com/data",
        "",  // 无效 URL
        "https://timeout.example.com/data",  // 超时
        "https://dns.invalid.com/data",      // DNS 失败
        "https://ssl.broken.com/data"        // SSL 错误
    };

    for (const auto& url : test_urls) {
        auto result = httpGet(url);
        if (result.is_ok()) {
            auto response = result.unwrap();
            std::cout << "✅ HTTP " << response.status_code << ": " << response.body << "\n";
        } else {
            std::cout << "❌ 网络错误: " << result.unwrap_err().Message() << "\n";
        }
    }

    // 4. void Result 演示
    std::cout << "\n=== 4. void Result 演示 ===\n";

    auto cache_result = cacheResponse(HttpResponse(200, "test data"));
    if (cache_result.is_ok()) {
        std::cout << "✅ 缓存操作成功\n";
    } else {
        std::cout << "❌ 缓存操作失败: " << cache_result.unwrap_err().Message() << "\n";
    }

    // 文件写入操作
    auto write_result = writeFile("/tmp/test.txt", "Hello, World!");
    if (write_result.is_ok()) {
        std::cout << "✅ 文件写入成功\n";
    } else {
        std::cout << "❌ 文件写入失败: " << write_result.unwrap_err().Message() << "\n";
    }

    // 5. 函数式链式操作演示
    std::cout << "\n=== 5. 函数式链式操作演示 ===\n";

    auto web_processing_result = processWebData("https://api.example.com/user");
    if (web_processing_result.is_ok()) {
        std::cout << "✅ Web数据处理成功: " << web_processing_result.unwrap() << "\n";
    } else {
        std::cout << "❌ Web数据处理失败: " << web_processing_result.unwrap_err().Message() << "\n";
    }

    // 文件备份操作
    auto backup_result = backupFile("/home/<USER>/important.txt", "/backup/important.txt");
    if (backup_result.is_ok()) {
        std::cout << "✅ 文件备份成功\n";
    } else {
        std::cout << "❌ 文件备份失败: " << backup_result.unwrap_err().Message() << "\n";
    }

    // 6. 错误传播和恢复演示
    std::cout << "\n=== 6. 错误传播和恢复演示 ===\n";

    // 批量文件处理 - 展示错误传播
    std::vector<std::string> file_paths = {"/file1.txt", "/file2.txt", "/nonexistent.txt"};
    auto batch_result = processFiles(file_paths);
    if (batch_result.is_ok()) {
        std::cout << "✅ 批量处理成功，处理了 " << batch_result.unwrap().size() << " 个文件\n";
    } else {
        std::cout << "❌ 批量处理失败: " << batch_result.unwrap_err().Message() << "\n";
    }

    // 错误恢复策略
    auto recovery_result = httpGet("")
        .or_else([](const ErrorCode& error) {
            std::cout << "尝试恢复网络错误: " << error.Message() << "\n";
            return httpGet("https://backup.api.com/data");
        });

    if (recovery_result.is_ok()) {
        std::cout << "✅ 错误恢复成功\n";
    } else {
        std::cout << "❌ 错误恢复失败: " << recovery_result.unwrap_err().Message() << "\n";
    }

    // 7. 比较操作演示
    std::cout << "\n=== 7. 比较操作演示 ===\n";

    Result<int> result1 = Ok(42);
    Result<int> result2 = Ok(42);
    Result<int> result3 = Ok(24);
    Result<int> fs_error_result = Err(FileSystemError::FILE_NOT_FOUND);
    Result<int> net_error_result = Err(NetworkError::TIMEOUT);

    std::cout << "result1 == result2: " << std::boolalpha << (result1 == result2) << "\n";
    std::cout << "result1 == result3: " << std::boolalpha << (result1 == result3) << "\n";
    std::cout << "result1 == 42: " << std::boolalpha << (result1 == 42) << "\n";
    std::cout << "fs_error == net_error: " << std::boolalpha << (fs_error_result == net_error_result) << "\n";

    // 8. 性能特性展示
    std::cout << "\n=== 8. 性能特性展示 ===\n";

    std::cout << "内存使用:\n";
    std::cout << "  sizeof(Result<int>): " << sizeof(Result<int>) << " bytes\n";
    std::cout << "  sizeof(Result<std::string>): " << sizeof(Result<std::string>) << " bytes\n";
    std::cout << "  sizeof(Result<void>): " << sizeof(Result<void>) << " bytes\n";
    std::cout << "  sizeof(Result<FileInfo>): " << sizeof(Result<FileInfo>) << " bytes\n";
    std::cout << "  sizeof(ErrorCode): " << sizeof(ErrorCode) << " bytes\n";

    // 性能测试
    const int iterations = 1000000;
    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < iterations; ++i) {
        Result<int> result = Ok(i);
        volatile bool is_ok = result.is_ok();
        (void)is_ok;
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);

    std::cout << "性能测试 (" << iterations << " 次迭代):\n";
    std::cout << "  总时间: " << duration.count() << " ns\n";
    std::cout << "  平均每次: " << duration.count() / iterations << " ns\n";

    // 9. 数学计算演示
    std::cout << "\n=== 9. 数学计算演示 ===\n";

    // 数学计算
    auto math_result = math_calculation();
    if (math_result.is_ok()) {
        std::cout << "数学计算结果: " << math_result.unwrap() << "\n";
    }

    // 除零错误处理
    auto division_error = safe_divide(10, 0);
    std::cout << "除零错误检测: " << std::boolalpha << division_error.is_err() << "\n";
    if (division_error.is_err()) {
        std::cout << "除零错误消息: " << division_error.unwrap_err().Message() << "\n";
    }

    // 10. 与 ErrorCode 的集成演示
    std::cout << "\n=== 10. 与 ErrorCode 的集成演示 ===\n";

    // ADL 自动转换
    Result<std::string> fs_result = Err(FileSystemError::PERMISSION_DENIED);
    Result<HttpResponse> net_result = Err(NetworkError::CONNECTION_FAILED);

    std::cout << "文件系统错误域: " << fs_result.unwrap_err().Domain().Name() << "\n";
    std::cout << "文件系统错误消息: " << fs_result.unwrap_err().Message() << "\n";
    std::cout << "网络错误域: " << net_result.unwrap_err().Domain().Name() << "\n";
    std::cout << "网络错误消息: " << net_result.unwrap_err().Message() << "\n";

    // 错误码值检查
    if (fs_result.is_err()) {
        auto error = fs_result.unwrap_err();
        std::cout << "错误码值: " << error.Value() << "\n";
        std::cout << "支持数据: " << error.SupportData() << "\n";
    }

    // 11. 最佳实践总结
    std::cout << "\n=== 11. 最佳实践总结 ===\n";
    std::cout << "✅ 使用 Err(枚举值) 而不是手动构造 ErrorCode\n";
    std::cout << "✅ 利用 ADL 自动错误码转换\n";
    std::cout << "✅ 使用 unwrap_or/unwrap_or_else 提供默认值\n";
    std::cout << "✅ 使用链式操作进行函数式编程\n";
    std::cout << "✅ 使用 expect 进行调试和开发\n";
    std::cout << "✅ 利用 void Result 处理无返回值操作\n";
    std::cout << "✅ 使用 or_else 进行错误恢复\n";
    std::cout << "✅ 利用编译时计算优化性能\n";

    std::cout << "\n=== 示例完成 ===\n";
    std::cout << "Result<T,E> 是一个生产级质量的错误处理库，提供：\n";
    std::cout << "• 类型安全的错误处理\n";
    std::cout << "• 零开销的性能特性\n";
    std::cout << "• 函数式编程支持\n";
    std::cout << "• 完整的 constexpr 支持\n";
    std::cout << "• 与 ErrorCode 的无缝集成\n";
    std::cout << "• 丰富的错误恢复策略\n";
    std::cout << "\n可以安全地用于任何生产环境的 C++ 项目中！\n";

    return 0;
}

// ============================================================================
// 示例输出说明：
//
// 本示例展示了 Result<T,E> 的所有核心功能：
// 1. 基本操作：Ok/Err 构造，is_ok/is_err 检查，unwrap/unwrap_err 访问
// 2. 安全访问：unwrap_or, unwrap_or_else, expect 等安全访问模式
// 3. 网络操作：跨错误域的错误处理
// 4. void Result：无返回值操作的错误处理
// 5. 链式操作：map, and_then, or_else 等函数式操作
// 6. 错误传播：在复杂业务逻辑中的错误传播和恢复
// 7. 比较操作：Result 之间的相等性比较
// 8. 性能特性：内存使用和运行时性能
// 9. constexpr 支持：编译时计算和优化
// 10. ErrorCode 集成：ADL 自动转换和错误域管理
// 11. 最佳实践：生产环境中的使用建议
//
// 这个示例可以作为在实际项目中使用 Result<T,E> 的完整参考。
// ============================================================================
