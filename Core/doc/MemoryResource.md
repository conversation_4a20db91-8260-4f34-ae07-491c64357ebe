### `ara::core::MemoryResource` 规范文档

#### **概述 (Overview)**

`ara::core::MemoryResource` 是一个抽象接口，它为一系列封装内存资源的类（例如 `ara::core::MonotonicBufferResource` 和 `ara::core::PolymorphicAllocator`）提供了统一的访问方式。该接口基于 C++17 [9] 和 C++20 [10] 标准中的 `std::pmr::memory_resource`。

> `ara::core::MemoryResource is an abstract interface to an unbounded set of classes (ara::core::MonotonicBufferResource and ara::core::PolymorphicAllocator) encapsulating memory resources. It is based on std::pmr::memory_resource from [9, the C++17 standard]/[10, the C++20 standard]`

---

### **详细规范 (Detailed Specifications)**

#### 1. SWS_CORE_11950: 基本行为 (Base Behavior)

**要求**：`ara::core::MemoryResource` 及其所有成员函数和相关辅助构件（如 `ara::core::MonotonicBufferResource` 和 `ara::core::PolymorphicAllocator`）的行为，应与 C++20 标准 `<memory_resource>` 头文件中的 `std::pmr::memory_resource` 保持一致，除非本文档中有特殊规定。

> `[SWS_CORE_11950]{DRAFT} MemoryResource base behavior ara::core:: MemoryResource and all its member functions and supporting constructs (ara:: core::MonotonicBufferResource and ara::core::PolymorphicAlloca tor) shall behave identical to those of std::pmr::memory_resource in header <memory_resource> from [10, the C++20 standard], except for the differences specified in this document. (RS_AP_00130)`

#### 2. SWS_CORE_11951: 错误处理行为 (Error Behavior)

**要求**：在遇到任何错误时，`ara::core::MemoryResource` 及其所有成员函数和相关辅助构件（如 `ara::core::MonotonicBufferResource` 和 `ara::core::PolymorphicAllocator`）应尽可能返回一个 `nullptr`。如果无法返回 `nullptr`，则应静默忽略该错误。

> `[SWS_CORE_11951]{DRAFT} MemoryResource error behavior ara::core:: MemoryResource and all its member functions and supporting constructs (ara:: core::MonotonicBufferResource and ara::core::PolymorphicAlloca tor) shall return a nullptr (if possible) in case of any error. Otherwise the error shall be silently ignored. (RS_AP_00130)`

**设计理由 (Rationale for SWS_CORE_11951):**

此设计旨在避免使用异常（Exceptions）。尽管一些 C++14 编译器支持向后兼容的 `[[nodiscard]]` 属性，但这并非标准行为，而是特定于编译器的实现。

> `Rationale for [SWS_CORE_11951]: Exceptions should be avoided. Some [4, the C++14 standard] compilers support a backport of [[nodiscard]] but this is compiler specific.` 