#include <gtest/gtest.h>
#include "../error_code.hpp"
#include "../error_domain.hpp"
#include "../error_types.hpp"
#include "../result.hpp"
#include <string>
#include <string_view>
#include <stdexcept>
#include <utility>
#include <chrono>

using namespace ara::core;

// 测试用错误枚举
enum class TestError : ErrorCodeType {
    SUCCESS = 0,
    INVALID_INPUT = 1,
    TIMEOUT = 2,
    NETWORK_ERROR = 3
};

// 测试用错误域
class TestErrorDomain final : public ErrorDomain {
public:
    constexpr TestErrorDomain() noexcept : ErrorDomain(0xC000123400000003) {}

    std::string_view Name() const noexcept override {
        return "TestErrorDomain";
    }

    std::string_view Message(CodeType errorCode) const noexcept override {
        switch (static_cast<TestError>(errorCode)) {
            case TestError::SUCCESS: return "Success";
            case TestError::INVALID_INPUT: return "Invalid input";
            case TestError::TIMEOUT: return "Timeout";
            case TestError::NETWORK_ERROR: return "Network error";
            default: return "Unknown error";
        }
    }

    void ThrowAsException(const ErrorCode& errorCode) const override {
        throw std::runtime_error(std::string(Message(errorCode.Value())));
    }
};

inline constexpr TestErrorDomain g_test_domain{};

constexpr ErrorCode MakeErrorCode(TestError e, ErrorSupportDataType data = ErrorSupportDataType()) noexcept {
    return ErrorCode(static_cast<ErrorCodeType>(e), g_test_domain, data);
}

class ErrTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// 基本 Err 功能测试
TEST_F(ErrTest, BasicEnumErrorCreation) {
    // 直接从枚举创建（推荐方式）
    Result<int> result = Err(TestError::INVALID_INPUT);

    EXPECT_TRUE(result.is_err());
    EXPECT_FALSE(result.is_ok());
    EXPECT_EQ(result.unwrap_err().Value(), static_cast<ErrorCodeType>(TestError::INVALID_INPUT));
}

TEST_F(ErrTest, StringErrorCreation) {
    Result<int, std::string> result = Err(std::string("Test error"));
    
    EXPECT_TRUE(result.is_err());
    EXPECT_EQ(result.unwrap_err(), "Test error");
}

TEST_F(ErrTest, IntegerErrorCreation) {
    Result<std::string, int> result = Err(42);
    
    EXPECT_TRUE(result.is_err());
    EXPECT_EQ(result.unwrap_err(), 42);
}

// 移动语义测试
TEST_F(ErrTest, StringMoveSemantics) {
    std::string error_msg = "Movable error message";
    std::string original_msg = error_msg;
    
    Result<int, std::string> result = Err(std::move(error_msg));
    
    EXPECT_TRUE(result.is_err());
    EXPECT_EQ(result.unwrap_err(), original_msg);
    EXPECT_TRUE(error_msg.empty()); // 原字符串应该被移动
}

TEST_F(ErrTest, LargeStringMove) {
    std::string large_msg(1000, 'A'); // 1000个字符的字符串
    std::string original = large_msg;
    
    Result<double, std::string> result = Err(std::move(large_msg));
    
    EXPECT_TRUE(result.is_err());
    EXPECT_EQ(result.unwrap_err(), original);
    EXPECT_TRUE(large_msg.empty());
}



// 链式操作测试
TEST_F(ErrTest, MapErrOperation) {
    Result<std::string> error_result = Err(TestError::TIMEOUT);

    auto mapped = error_result.map_err([](const ErrorCode& err) {
        return std::string("Mapped: ") + std::string(err.Message());
    });

    EXPECT_TRUE(mapped.is_err());
    EXPECT_EQ(mapped.unwrap_err(), "Mapped: Timeout");
}

TEST_F(ErrTest, OrElseOperation) {
    Result<std::string> error_result = Err(TestError::TIMEOUT);

    auto recovered = error_result.or_else([](const ErrorCode&) -> Result<std::string> {
        return Ok(std::string("Recovered"));
    });

    EXPECT_TRUE(recovered.is_ok());
    EXPECT_EQ(recovered.unwrap(), "Recovered");
}

// void Result 测试
TEST_F(ErrTest, VoidResultError) {
    Result<void> void_err = Err(TestError::TIMEOUT);

    EXPECT_TRUE(void_err.is_err());
    EXPECT_FALSE(void_err.is_ok());
    EXPECT_EQ(void_err.unwrap_err().Value(), static_cast<ErrorCodeType>(TestError::TIMEOUT));
}

TEST_F(ErrTest, VoidResultSuccess) {
    Result<void> void_ok = Ok();
    
    EXPECT_TRUE(void_ok.is_ok());
    EXPECT_FALSE(void_ok.is_err());
}

// 不同错误类型测试
TEST_F(ErrTest, DifferentErrorTypes) {
    // 整数错误
    Result<std::string, int> int_err = Err(42);
    EXPECT_TRUE(int_err.is_err());
    EXPECT_EQ(int_err.unwrap_err(), 42);
    
    // 字符串错误
    Result<int, std::string> str_err = Err(std::string("String error"));
    EXPECT_TRUE(str_err.is_err());
    EXPECT_EQ(str_err.unwrap_err(), "String error");
    
    // ErrorCode 错误（从枚举）
    Result<double> ec_err = Err(TestError::INVALID_INPUT);
    EXPECT_TRUE(ec_err.is_err());
    EXPECT_EQ(ec_err.unwrap_err().Value(), static_cast<ErrorCodeType>(TestError::INVALID_INPUT));
}

// Err 与 Ok 互操作性测试
TEST_F(ErrTest, ErrOkInteroperability) {
    // 测试 Err 可以转换为正确的 Result 类型
    Result<std::string> err_result = Err(TestError::INVALID_INPUT);
    Result<std::string> ok_result = Ok(std::string("Success"));

    EXPECT_TRUE(err_result.is_err());
    EXPECT_TRUE(ok_result.is_ok());

    // 测试类型兼容性
    EXPECT_EQ(typeid(err_result), typeid(ok_result));
}

// 性能测试
TEST_F(ErrTest, PerformanceTest) {
    const int iterations = 10000;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; ++i) {
        Result<int> result = Err(TestError::INVALID_INPUT);
        // 防止编译器优化掉
        volatile bool is_err = result.is_err();
        (void)is_err;
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    // 每次 Err 创建应该在合理时间内完成（这里设置为 1 微秒）
    EXPECT_LT(duration.count() / iterations, 1000); // 小于 1000 纳秒
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
