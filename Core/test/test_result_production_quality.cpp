#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <string>
#include "../result.hpp"
#include "../error_code.hpp"
#include "../error_domain.hpp"
#include "../error_types.hpp"

using namespace ara::core;

// 测试用错误枚举
enum class TestError : ErrorCodeType {
    SUCCESS = 0,
    INVALID_INPUT = 1,
    TIMEOUT = 2,
    NETWORK_ERROR = 3
};

// 测试用错误域
class TestErrorDomain final : public ErrorDomain {
public:
    constexpr TestErrorDomain() noexcept : ErrorDomain(0xC000123400000001) {}

    std::string_view Name() const noexcept override {
        return "TestErrorDomain";
    }

    std::string_view Message(CodeType errorCode) const noexcept override {
        switch (static_cast<TestError>(errorCode)) {
            case TestError::SUCCESS: return "Success";
            case TestError::INVALID_INPUT: return "Invalid input";
            case TestError::TIMEOUT: return "Timeout";
            case TestError::NETWORK_ERROR: return "Network error";
            default: return "Unknown error";
        }
    }

    void ThrowAsException(const ErrorCode& errorCode) const override {
        throw std::runtime_error(std::string(Message(errorCode.Value())));
    }
};

inline constexpr TestErrorDomain g_test_domain{};

constexpr ErrorCode MakeErrorCode(TestError e, ErrorSupportDataType data = ErrorSupportDataType()) noexcept {
    return ErrorCode(static_cast<ErrorCodeType>(e), g_test_domain, data);
}

class ResultProductionQualityTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// 基本功能测试
TEST_F(ResultProductionQualityTest, BasicConstruction) {
    // 成功构造
    Result<int> ok_result = Ok(42);
    EXPECT_TRUE(ok_result.is_ok());
    EXPECT_FALSE(ok_result.is_err());
    EXPECT_EQ(ok_result.unwrap(), 42);

    // 错误构造
    Result<int> err_result = Err(TestError::INVALID_INPUT);
    EXPECT_FALSE(err_result.is_ok());
    EXPECT_TRUE(err_result.is_err());
    EXPECT_EQ(err_result.unwrap_err().Value(), static_cast<ErrorCodeType>(TestError::INVALID_INPUT));
}

// 类型安全测试
TEST_F(ResultProductionQualityTest, TypeSafety) {
    // 确保不会意外构造错误的类型
    Result<int> result = Ok(42);
    EXPECT_TRUE(result.is_ok());
    
    // 测试SFINAE约束
    static_assert(std::is_constructible_v<Result<int>, int>);
    static_assert(!std::is_constructible_v<Result<int>, ErrorCode>);  // 不应该能直接构造
}

// 移动语义测试
TEST_F(ResultProductionQualityTest, MoveSemantics) {
    std::vector<int> large_vec(1000, 42);
    auto original_size = large_vec.size();
    
    // 移动构造
    auto result = Ok(std::move(large_vec));
    EXPECT_TRUE(result.is_ok());
    EXPECT_TRUE(large_vec.empty());  // 原对象应该被移动
    
    // 移动访问
    auto moved_vec = std::move(result).unwrap();
    EXPECT_EQ(moved_vec.size(), original_size);
}

// 异常安全测试
TEST_F(ResultProductionQualityTest, ExceptionSafety) {
    auto ok_result = Ok(42);
    auto err_result = Err(TestError::INVALID_INPUT);
    
    // unwrap 在错误时应该抛出异常
    EXPECT_NO_THROW(ok_result.unwrap());
    EXPECT_THROW(err_result.unwrap(), std::runtime_error);
    
    // unwrap_err 在成功时应该抛出异常
    EXPECT_THROW(ok_result.unwrap_err(), std::runtime_error);
    EXPECT_NO_THROW(err_result.unwrap_err());
    
    // expect 方法测试
    EXPECT_NO_THROW(ok_result.expect("Should not throw"));
    EXPECT_THROW(err_result.expect("Should throw"), std::runtime_error);
    
    // expect_err 方法测试
    EXPECT_THROW(ok_result.expect_err("Should throw"), std::runtime_error);
    EXPECT_NO_THROW(err_result.expect_err("Should not throw"));
}

// 链式操作测试
TEST_F(ResultProductionQualityTest, ChainingOperations) {
    auto divide = [](int a, int b) -> Result<int> {
        if (b == 0) return Err(TestError::INVALID_INPUT);
        return Ok(a / b);
    };
    
    // 成功链式操作
    auto result = divide(100, 2)
        .map([](int x) { return x * 2; })
        .and_then([&](int x) { return divide(x, 4); })
        .map([](int x) { return x + 10; });
    
    EXPECT_TRUE(result.is_ok());
    EXPECT_EQ(result.unwrap(), 35);  // ((100/2)*2)/4 + 10 = 35
    
    // 错误链式操作
    auto error_result = divide(100, 0)
        .map([](int x) { return x * 2; })
        .and_then([&](int x) { return divide(x, 4); })
        .map([](int x) { return x + 10; });
    
    EXPECT_TRUE(error_result.is_err());
}

// 比较操作测试
TEST_F(ResultProductionQualityTest, ComparisonOperations) {
    Result<int> ok1 = Ok(42);
    Result<int> ok2 = Ok(42);
    Result<int> ok3 = Ok(43);
    Result<int> err1 = Err(TestError::INVALID_INPUT);
    Result<int> err2 = Err(TestError::INVALID_INPUT);
    Result<int> err3 = Err(TestError::TIMEOUT);

    // 相等比较
    EXPECT_TRUE(ok1 == ok2);
    EXPECT_FALSE(ok1 == ok3);
    EXPECT_TRUE(err1 == err2);
    EXPECT_FALSE(err1 == err3);
    EXPECT_FALSE(ok1 == err1);

    // 不等比较
    EXPECT_FALSE(ok1 != ok2);
    EXPECT_TRUE(ok1 != ok3);

    // 与值直接比较
    EXPECT_TRUE(ok1 == 42);
    EXPECT_FALSE(ok1 == 43);
    EXPECT_FALSE(err1 == 42);
}

// void 特化测试
TEST_F(ResultProductionQualityTest, VoidSpecialization) {
    auto ok_void = Result<void>();
    auto err_void = Result<void>(std::in_place_index<1>, TestError::TIMEOUT);
    
    EXPECT_TRUE(ok_void.is_ok());
    EXPECT_FALSE(ok_void.is_err());
    EXPECT_NO_THROW(ok_void.unwrap());
    
    EXPECT_FALSE(err_void.is_ok());
    EXPECT_TRUE(err_void.is_err());
    EXPECT_THROW(err_void.unwrap(), std::runtime_error);
}

// 性能基准测试
TEST_F(ResultProductionQualityTest, PerformanceBenchmark) {
    const int iterations = 1000000;
    
    // 构造性能测试
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < iterations; ++i) {
        volatile auto result = Ok(i);
        (void)result;
    }
    auto end = std::chrono::high_resolution_clock::now();
    auto construction_time = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    // 访问性能测试
    Result<int> ok_result = Ok(42);
    start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < iterations; ++i) {
        volatile bool is_ok = ok_result.is_ok();
        (void)is_ok;
    }
    end = std::chrono::high_resolution_clock::now();
    auto access_time = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    std::cout << "Construction time: " << construction_time.count() << " ns for " << iterations << " iterations\n";
    std::cout << "Access time: " << access_time.count() << " ns for " << iterations << " iterations\n";
    
    // 性能应该在合理范围内（每次操作少于100纳秒）
    EXPECT_LT(construction_time.count() / iterations, 100);
    EXPECT_LT(access_time.count() / iterations, 10);
}

// 内存布局测试
TEST_F(ResultProductionQualityTest, MemoryLayout) {
    // Result 应该是紧凑的内存布局
    EXPECT_LE(sizeof(Result<int>), sizeof(std::variant<int, ErrorCode>) + 8);
    EXPECT_LE(sizeof(Result<void>), sizeof(std::variant<std::monostate, ErrorCode>) + 8);
    
    std::cout << "sizeof(Result<int>): " << sizeof(Result<int>) << " bytes\n";
    std::cout << "sizeof(Result<void>): " << sizeof(Result<void>) << " bytes\n";
    std::cout << "sizeof(ErrorCode): " << sizeof(ErrorCode) << " bytes\n";
}

// constexpr 支持测试
TEST_F(ResultProductionQualityTest, ConstexprSupport) {
    // 基本的 constexpr 支持
    Result<int> ok_result = Ok(42);
    EXPECT_TRUE(ok_result.is_ok());
    EXPECT_FALSE(ok_result.is_err());
    EXPECT_EQ(ok_result.unwrap(), 42);
}

// 交换操作测试
TEST_F(ResultProductionQualityTest, SwapOperations) {
    Result<int> result1 = Ok(42);
    Result<int> result2 = Ok(24);

    result1.swap(result2);

    EXPECT_EQ(result1.unwrap(), 24);
    EXPECT_EQ(result2.unwrap(), 42);

    // 使用 ADL swap
    using std::swap;
    swap(result1, result2);

    EXPECT_EQ(result1.unwrap(), 42);
    EXPECT_EQ(result2.unwrap(), 24);
}

// 错误恢复测试
TEST_F(ResultProductionQualityTest, ErrorRecovery) {
    auto divide = [](int a, int b) -> Result<int> {
        if (b == 0) return Err(TestError::INVALID_INPUT);
        return Ok(a / b);
    };
    
    // or_else 错误恢复
    Result<int> result = divide(100, 0)
        .or_else([](const ErrorCode&) { return Ok(42); });

    EXPECT_TRUE(result.is_ok());
    EXPECT_EQ(result.unwrap(), 42);
    
    // unwrap_or 默认值
    auto value = divide(100, 0).unwrap_or(99);
    EXPECT_EQ(value, 99);
    
    // unwrap_or_else 函数计算默认值
    auto computed_value = divide(100, 0).unwrap_or_else([](const ErrorCode&) { return 88; });
    EXPECT_EQ(computed_value, 88);
}
