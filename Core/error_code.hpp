/*
 * Copyright (c) 2024 leehaonan <<EMAIL>>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * @file           : error_code.hpp
 * <AUTHOR> leehaonan
 * @brief          : 对Autosar_AP_SWS_Core中的ErrorCode进行实现
 * @date           : 2024/6/25
 * @version        : R23-11
 */

#ifndef THREADPRACTISEYEEAH_ERROR_CODE_HPP
#define THREADPRACTISEYEEAH_ERROR_CODE_HPP

#include <string_view>
#include <stdexcept>
#include <string>
#include <type_traits>
#include "error_types.hpp"
#include "error_domain.hpp"

namespace ara::core
{
    // Forward declaration
    class ErrorDomain;



    class ErrorCode final
    {
    public:
        // Implementation - [SWS_CORE_00512]
        // This constructor only participates in overload resolution when EnumT is an enum type
        template <typename EnumT,
                  typename = std::enable_if_t<std::is_enum_v<EnumT>>>
        explicit constexpr ErrorCode(EnumT e, ErrorSupportDataType data = ErrorSupportDataType()) noexcept;

        // Implementation - [SWS_CORE_00513]
        constexpr ErrorCode(ErrorCodeType value, const ErrorDomain& domain,
                           ErrorSupportDataType data = ErrorSupportDataType()) noexcept
            : m_errorCodeValue(value)
            , m_errorDomain(domain)
            , m_supportData(data)
            , m_message{}
        {}

        // Copy constructor
        ErrorCode(const ErrorCode& other) noexcept = default;

        // Move constructor - optimized for performance
        ErrorCode(ErrorCode&& other) noexcept = default;

        // Assignment operators are deleted due to const reference member
        // This is intentional as per AUTOSAR specification
        ErrorCode& operator=(const ErrorCode& other) = delete;
        ErrorCode& operator=(ErrorCode&& other) = delete;

        ~ErrorCode() = default;

        // Implementation - [SWS_CORE_00514]
        [[nodiscard]] constexpr ErrorCodeType Value() const noexcept { return m_errorCodeValue; }

        // Implementation - [SWS_CORE_00515]
        [[nodiscard]] constexpr const ErrorDomain& Domain() const noexcept { return m_errorDomain; }

        // Implementation - [SWS_CORE_00516]
        [[nodiscard]] constexpr ErrorSupportDataType SupportData() const noexcept { return m_supportData; }

        // Get cached message or generate from domain
        [[nodiscard]] std::string_view Message() const noexcept{
            return m_message.empty() ? m_errorDomain.Message(m_errorCodeValue) : m_message;
        }

        // Set custom error message - caller must ensure lifetime
        void setErrorMessage(std::string_view message) noexcept { m_message = message; }

        // Implementation - [SWS_CORE_00519]
        void ThrowAsException() const{
            this->Domain().ThrowAsException(*this);
        }

        // Implementation - [SWS_CORE_00571]
        friend bool operator==(const ErrorCode& lhs, const ErrorCode& rhs) noexcept{
            return lhs.Value() == rhs.Value() && lhs.Domain() == rhs.Domain();
        }

        // Implementation - [SWS_CORE_00572]
        friend bool operator!=(const ErrorCode& lhs, const ErrorCode& rhs) noexcept{
            return !(lhs == rhs);
        }


    private:
        ErrorCodeType m_errorCodeValue;
        const ErrorDomain& m_errorDomain;
        ErrorSupportDataType m_supportData;
        std::string_view m_message;
    };



    // Template implementation - [SWS_CORE_10990] Convenience constructor
    // Uses ADL (Argument Dependent Lookup) to find appropriate MakeErrorCode function
    // Only participates in overload resolution when EnumT is an enum type
    template <typename EnumT, typename>
    constexpr ErrorCode::ErrorCode(EnumT e, ErrorSupportDataType data) noexcept
        : ErrorCode(MakeErrorCode(e, data))  // Delegate to MakeErrorCode via ADL
    {
    }

    // Implementation - [SWS_CORE_00513] Factory function with explicit domain
    // This is the fundamental factory function that all domain-specific MakeErrorCode functions should use
    // constexpr ErrorCode MakeErrorCode(ErrorCodeType value, const ErrorDomain& domain,
    //                                  ErrorSupportDataType data) noexcept
    // {
    //     return ErrorCode(value, domain, data);
    // }


}  // namespace ara::core


#endif  // THREADPRACTISEYEEAH_ERROR_CODE_HPP
