#pragma once

#include "result.hpp"

namespace ara::core {



/**
 * @brief Pre-Initialization of the ARA Framework.
 * @return error code
 * @note Do not call in the constructor of a static object
 * @note Do not call during global variable initialization
 */
Result<void> Initialize () noexcept{


}

/**
 * @brief De-Initialization of the ARA Framework.
 * @return error code
 */
Result<void> Deinitialize () noexcept;


} // namespace ara::core