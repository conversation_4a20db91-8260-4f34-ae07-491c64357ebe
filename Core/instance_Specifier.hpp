//
// Created by 15531 on 2025/4/21.
//

#ifndef INSTANCE_SPECIFIER_HPP
#define INSTANCE_SPECIFIER_HPP


#include <string_view>
#include <string>


namespace aether::core {
    using StringView = std::string_view;
    class InstanceSpecifier final {
    public:

        explicit InstanceSpecifier(StringView meta_model_identifier)
            : path_(meta_model_identifier) {
            ValidateSyntax(meta_model_identifier);
        }

        // static aether::core::ResultOr<InstanceSpecifier> Create(StringView meta_model_identifier) noexcept {
        //     try {
        //         ValidateSyntax(meta_model_identifier);
        //         return InstanceSpecifier(meta_model_identifier);
        //     } catch (const std::exception& e) {
        //         return absl::InvalidArgumentError("Invalid InstanceSpecifier format");
        //     }
        // }

        // 比较运算符（保持noexcept）
        bool operator==(const InstanceSpecifier& other) const noexcept {
            return path_ == other.path_;
        }

        // 移动操作保持noexcept
        InstanceSpecifier(InstanceSpecifier&& other) noexcept = default;
        InstanceSpecifier& operator=(InstanceSpecifier&& other) noexcept = default;

        // 字符串转换接口
        StringView ToString() const noexcept {
            return path_;
        }

    private:
        std::string path_;

        // 语法验证（抛出标准异常）
        static void ValidateSyntax(StringView identifier) {
            if (identifier.empty() || identifier[0] != '/') {
                throw std::invalid_argument("Invalid path format");
            }
        }
    };

} // namespace aether::core





#endif //INSTANCE_SPECIFIER_HPP
