#ifndef RESULT_HPP_
#define RESULT_HPP_

#include <variant>
#include <type_traits>
#include <utility>
#include <stdexcept>
#include <exception>
#include <string>
#include <cstdint>
#include "error_code.hpp"
namespace ara::core {

// 前向声明
class ErrorCode;
class ErrorDomain;
using ErrorCodeType = std::int32_t;
using ErrorSupportDataType = std::int32_t;

// 前向声明Result类模板
template<typename T, typename E>
class Result;

// 默认错误类型为ErrorCode
template<typename T, typename E = ErrorCode>
class Result {
private:
    std::variant<T, E> data_;

    // 内部辅助函数 - 使用variant的index而不是额外的bool字段
    [[nodiscard]] constexpr bool holds_value() const noexcept {
        return data_.index() == 0;
    }

    [[nodiscard]] constexpr bool holds_error() const noexcept {
        return data_.index() == 1;
    }

public:
    using value_type = T;
    using error_type = E;

    // 静态断言确保类型安全
    static_assert(!std::is_reference_v<T>, "Result value type cannot be a reference");
    static_assert(!std::is_reference_v<E>, "Result error type cannot be a reference");
    static_assert(!std::is_void_v<T>, "Use Result<void, E> specialization for void types");

    // 从值构造 - 成功情况（严格的SFINAE约束确保类型安全）
    template<typename U,
             typename = std::enable_if_t<
                 std::is_constructible_v<T, U&&> &&
                 !std::is_same_v<std::decay_t<U>, Result> &&
                 !std::is_same_v<std::decay_t<U>, std::in_place_index_t<1>> &&
                 !std::is_convertible_v<U&&, E> &&  // 防止意外转换为错误类型
                 !std::is_same_v<std::decay_t<U>, E>>>  // 防止直接传入错误类型
    constexpr Result(U&& value) noexcept(std::is_nothrow_constructible_v<T, U&&>)
        : data_(std::in_place_index<0>, std::forward<U>(value)) {}

    // 从错误构造 - 失败情况（显式使用标签避免歧义）
    template<typename U,
             typename = std::enable_if_t<std::is_constructible_v<E, U&&>>>
    constexpr Result(std::in_place_index_t<1>, U&& error) noexcept(std::is_nothrow_constructible_v<E, U&&>)
        : data_(std::in_place_index<1>, std::forward<U>(error)) {}

    // 移动构造
    constexpr Result(Result&& other) noexcept = default;

    // 拷贝构造
    constexpr Result(const Result& other) = default;

    // 移动赋值
    constexpr Result& operator=(Result&& other) noexcept = default;

    // 拷贝赋值
    constexpr Result& operator=(const Result& other) = default;

    // 析构函数
    ~Result() = default;

    // 状态检查
    [[nodiscard]] constexpr bool is_ok() const noexcept { return holds_value(); }
    [[nodiscard]] constexpr bool is_err() const noexcept { return holds_error(); }

    // 显式bool转换
    [[nodiscard]] constexpr explicit operator bool() const noexcept { return is_ok(); }

    // 获取值 - const左值引用版本（强异常安全）
    [[nodiscard]] constexpr const T& unwrap() const& {
        if (is_err()) [[unlikely]] {
            throw std::runtime_error("Called unwrap() on an Err value");
        }
        return std::get<0>(data_);
    }

    // 获取值 - 左值引用版本
    [[nodiscard]] constexpr T& unwrap() & {
        if (is_err()) [[unlikely]] {
            throw std::runtime_error("Called unwrap() on an Err value");
        }
        return std::get<0>(data_);
    }

    // 获取值 - 右值引用版本
    [[nodiscard]] constexpr T&& unwrap() && {
        if (is_err()) [[unlikely]] {
            throw std::runtime_error("Called unwrap() on an Err value");
        }
        return std::move(std::get<0>(data_));
    }

    // 获取值 - const右值引用版本
    [[nodiscard]] constexpr const T&& unwrap() const&& {
        if (is_err()) {
            throw std::runtime_error("Called unwrap() on an Err value");
        }
        return std::move(std::get<0>(data_));
    }

    // 获取错误 - const左值引用版本
    [[nodiscard]] constexpr const E& unwrap_err() const& {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::get<1>(data_);
    }

    // 获取错误 - 左值引用版本
    [[nodiscard]] constexpr E& unwrap_err() & {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::get<1>(data_);
    }

    // 获取错误 - 右值引用版本
    [[nodiscard]] constexpr E&& unwrap_err() && {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::move(std::get<1>(data_));
    }

    // 获取错误 - const右值引用版本
    [[nodiscard]] constexpr const E&& unwrap_err() const&& {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::move(std::get<1>(data_));
    }

    // 如果有错误则抛出异常
    constexpr void throw_if_err() const {
        if (is_err()) {
            if constexpr (std::is_base_of_v<std::exception, E>) {
                throw std::get<1>(data_);
            } else {
                throw std::runtime_error("Error in Result");
            }
        }
    }

    // expect - 带自定义错误消息的unwrap
    [[nodiscard]] constexpr const T& expect(const char* msg) const& {
        if (is_err()) {
            throw std::runtime_error(msg);
        }
        return std::get<0>(data_);
    }

    [[nodiscard]] constexpr T& expect(const char* msg) & {
        if (is_err()) {
            throw std::runtime_error(msg);
        }
        return std::get<0>(data_);
    }

    [[nodiscard]] constexpr T&& expect(const char* msg) && {
        if (is_err()) {
            throw std::runtime_error(msg);
        }
        return std::move(std::get<0>(data_));
    }

    // expect_err - 带自定义错误消息的unwrap_err
    [[nodiscard]] constexpr const E& expect_err(const char* msg) const& {
        if (is_ok()) {
            throw std::runtime_error(msg);
        }
        return std::get<1>(data_);
    }

    [[nodiscard]] constexpr E& expect_err(const char* msg) & {
        if (is_ok()) {
            throw std::runtime_error(msg);
        }
        return std::get<1>(data_);
    }

    [[nodiscard]] constexpr E&& expect_err(const char* msg) && {
        if (is_ok()) {
            throw std::runtime_error(msg);
        }
        return std::move(std::get<1>(data_));
    }

    // 检查是否包含特定值
    template<typename U>
    [[nodiscard]] constexpr bool contains(const U& value) const {
        return is_ok() && std::get<0>(data_) == value;
    }

    // 检查是否包含特定错误
    template<typename F>
    [[nodiscard]] constexpr bool contains_err(const F& error) const {
        return is_err() && std::get<1>(data_) == error;
    }

    // 交换两个Result对象（强异常安全保证）
    constexpr void swap(Result& other) noexcept(
        std::is_nothrow_swappable_v<T> && std::is_nothrow_swappable_v<E>) {
        data_.swap(other.data_);
    }

    // 重置为默认构造的值状态
    template<typename... Args>
    constexpr void emplace(Args&&... args) noexcept(std::is_nothrow_constructible_v<T, Args...>) {
        data_.template emplace<0>(std::forward<Args>(args)...);
    }

    // 重置为错误状态
    template<typename... Args>
    constexpr void emplace_err(Args&&... args) noexcept(std::is_nothrow_constructible_v<E, Args...>) {
        data_.template emplace<1>(std::forward<Args>(args)...);
    }

    // 安全的值访问指针
    [[nodiscard]] constexpr const T* operator->() const noexcept {
        return is_ok() ? &std::get<0>(data_) : nullptr;
    }

    [[nodiscard]] constexpr T* operator->() noexcept {
        return is_ok() ? &std::get<0>(data_) : nullptr;
    }

    // 解引用操作符（仅在is_ok()时安全）
    [[nodiscard]] constexpr const T& operator*() const& noexcept {
        return std::get<0>(data_);
    }

    [[nodiscard]] constexpr T& operator*() & noexcept {
        return std::get<0>(data_);
    }

    [[nodiscard]] constexpr T&& operator*() && noexcept {
        return std::move(std::get<0>(data_));
    }

    [[nodiscard]] constexpr const T&& operator*() const&& noexcept {
        return std::move(std::get<0>(data_));
    }

    // unwrap_or - 提供默认值
    template<typename U>
    [[nodiscard]] constexpr T unwrap_or(U&& default_value) const& {
        if (is_ok()) {
            return std::get<0>(data_);
        }
        return static_cast<T>(std::forward<U>(default_value));
    }

    // 移动版本的unwrap_or
    template<typename U>
    [[nodiscard]] constexpr T unwrap_or(U&& default_value) && {
        if (is_ok()) {
            return std::move(std::get<0>(data_));
        }
        return static_cast<T>(std::forward<U>(default_value));
    }

    // unwrap_or_else - 使用函数计算默认值
    template<typename F>
    [[nodiscard]] constexpr T unwrap_or_else(F&& f) const& {
        if (is_ok()) {
            return std::get<0>(data_);
        }
        return std::forward<F>(f)(std::get<1>(data_));
    }

    // 移动版本的unwrap_or_else
    template<typename F>
    [[nodiscard]] constexpr T unwrap_or_else(F&& f) && {
        if (is_ok()) {
            return std::move(std::get<0>(data_));
        }
        return std::forward<F>(f)(std::move(std::get<1>(data_)));
    }

    // map - 转换Ok值，保持Err不变
    template<typename F>
    [[nodiscard]] constexpr auto map(F&& f) const& {
        using U = std::invoke_result_t<F, const T&>;
        if (is_ok()) {
            return Result<U, E>(std::forward<F>(f)(std::get<0>(data_)));
        }
        return Result<U, E>(std::in_place_index<1>, std::get<1>(data_));
    }

    // 移动版本的map
    template<typename F>
    [[nodiscard]] constexpr auto map(F&& f) && {
        using U = std::invoke_result_t<F, T&&>;
        if (is_ok()) {
            return Result<U, E>(std::forward<F>(f)(std::move(std::get<0>(data_))));
        }
        return Result<U, E>(std::in_place_index<1>, std::move(std::get<1>(data_)));
    }

    // map_err - 转换Err值，保持Ok不变
    template<typename F>
    [[nodiscard]] constexpr auto map_err(F&& f) const& {
        using U = std::invoke_result_t<F, const E&>;
        if (is_err()) {
            return Result<T, U>(std::in_place_index<1>, std::forward<F>(f)(std::get<1>(data_)));
        }
        return Result<T, U>(std::get<0>(data_));
    }

    // 移动版本的map_err
    template<typename F>
    [[nodiscard]] constexpr auto map_err(F&& f) && {
        using U = std::invoke_result_t<F, E&&>;
        if (is_err()) {
            return Result<T, U>(std::in_place_index<1>, std::forward<F>(f)(std::move(std::get<1>(data_))));
        }
        return Result<T, U>(std::move(std::get<0>(data_)));
    }

    // and_then - 链式操作，类似flatMap
    template<typename F>
    [[nodiscard]] constexpr auto and_then(F&& f) const& {
        using ResultType = std::invoke_result_t<F, const T&>;
        if (is_ok()) {
            return std::forward<F>(f)(std::get<0>(data_));
        }
        return ResultType(std::in_place_index<1>, std::get<1>(data_));
    }

    // 移动版本的and_then
    template<typename F>
    [[nodiscard]] constexpr auto and_then(F&& f) && {
        using ResultType = std::invoke_result_t<F, T&&>;
        if (is_ok()) {
            return std::forward<F>(f)(std::move(std::get<0>(data_)));
        }
        return ResultType(std::in_place_index<1>, std::move(std::get<1>(data_)));
    }

    // or_else - 错误恢复
    template<typename F>
    [[nodiscard]] constexpr auto or_else(F&& f) const& {
        using ResultType = std::invoke_result_t<F, const E&>;
        if (is_err()) {
            return std::forward<F>(f)(std::get<1>(data_));
        }
        return ResultType(std::get<0>(data_));
    }

    // 移动版本的or_else
    template<typename F>
    [[nodiscard]] constexpr auto or_else(F&& f) && {
        using ResultType = std::invoke_result_t<F, E&&>;
        if (is_err()) {
            return std::forward<F>(f)(std::move(std::get<1>(data_)));
        }
        return ResultType(std::move(std::get<0>(data_)));
    }

    // 比较操作符 - 支持完整的值语义
    template<typename U, typename F>
    [[nodiscard]] constexpr bool operator==(const Result<U, F>& other) const {
        if (is_ok() && other.is_ok()) {
            return std::get<0>(data_) == std::get<0>(other.data_);
        }
        if (is_err() && other.is_err()) {
            return std::get<1>(data_) == std::get<1>(other.data_);
        }
        return false;
    }

    template<typename U, typename F>
    [[nodiscard]] constexpr bool operator!=(const Result<U, F>& other) const {
        return !(*this == other);
    }

    // 与值直接比较
    template<typename U>
    [[nodiscard]] constexpr bool operator==(const U& value) const {
        return is_ok() && std::get<0>(data_) == value;
    }

    template<typename U>
    [[nodiscard]] constexpr bool operator!=(const U& value) const {
        return !(*this == value);
    }

};

// Result<void, E> 模板特化 - 处理不返回值的操作结果
template<typename E>
class Result<void, E> {
private:
    std::variant<std::monostate, E> data_;

    // 内部辅助函数
    [[nodiscard]] constexpr bool holds_value() const noexcept {
        return data_.index() == 0;
    }

    [[nodiscard]] constexpr bool holds_error() const noexcept {
        return data_.index() == 1;
    }

public:
    using value_type = void;
    using error_type = E;

    // 静态断言确保类型安全
    static_assert(!std::is_reference_v<E>, "Result error type cannot be a reference");

    // 默认构造 - 成功情况
    constexpr Result() noexcept : data_(std::in_place_index<0>) {}

    // 从错误构造 - 失败情况
    template<typename U,
             typename = std::enable_if_t<std::is_constructible_v<E, U&&>>>
    constexpr Result(std::in_place_index_t<1>, U&& error) noexcept(std::is_nothrow_constructible_v<E, U&&>)
        : data_(std::in_place_index<1>, std::forward<U>(error)) {}

    // 拷贝和移动构造/赋值
    constexpr Result(const Result& other) = default;
    constexpr Result(Result&& other) noexcept = default;
    constexpr Result& operator=(const Result& other) = default;
    constexpr Result& operator=(Result&& other) noexcept = default;

    // 析构函数
    ~Result() = default;

    // 状态检查
    [[nodiscard]] constexpr bool is_ok() const noexcept { return holds_value(); }
    [[nodiscard]] constexpr bool is_err() const noexcept { return holds_error(); }

    // 显式bool转换
    [[nodiscard]] constexpr explicit operator bool() const noexcept { return is_ok(); }

    // unwrap - void版本不返回值，只检查状态
    constexpr void unwrap() const {
        if (is_err()) {
            throw std::runtime_error("Called unwrap() on an Err value");
        }
    }

    // 获取错误 - const左值引用版本
    [[nodiscard]] constexpr const E& unwrap_err() const& {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::get<1>(data_);
    }

    // 获取错误 - 左值引用版本
    [[nodiscard]] constexpr E& unwrap_err() & {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::get<1>(data_);
    }

    // 获取错误 - 右值引用版本
    [[nodiscard]] constexpr E&& unwrap_err() && {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::move(std::get<1>(data_));
    }

    // 获取错误 - const右值引用版本
    [[nodiscard]] constexpr const E&& unwrap_err() const&& {
        if (is_ok()) {
            throw std::runtime_error("Called unwrap_err() on an Ok value");
        }
        return std::move(std::get<1>(data_));
    }

    // 如果有错误则抛出异常
    constexpr void throw_if_err() const {
        if (is_err()) {
            if constexpr (std::is_base_of_v<std::exception, E>) {
                throw std::get<1>(data_);
            } else {
                throw std::runtime_error("Error in Result");
            }
        }
    }

    // map - 转换为其他类型的Result
    template<typename F>
    [[nodiscard]] constexpr auto map(F&& f) const {
        using U = std::invoke_result_t<F>;
        if (is_ok()) {
            if constexpr (std::is_void_v<U>) {
                std::forward<F>(f)();
                return Result<void, E>();
            } else {
                return Result<U, E>(std::forward<F>(f)());
            }
        }
        return Result<U, E>(std::in_place_index<1>, std::get<1>(data_));
    }

    // map_err - 转换Err值，保持Ok不变
    template<typename F>
    [[nodiscard]] constexpr auto map_err(F&& f) const& {
        using U = std::invoke_result_t<F, const E&>;
        if (is_err()) {
            return Result<void, U>(std::in_place_index<1>, std::forward<F>(f)(std::get<1>(data_)));
        }
        return Result<void, U>();
    }

    // 移动版本的map_err
    template<typename F>
    [[nodiscard]] constexpr auto map_err(F&& f) && {
        using U = std::invoke_result_t<F, E&&>;
        if (is_err()) {
            return Result<void, U>(std::in_place_index<1>, std::forward<F>(f)(std::move(std::get<1>(data_))));
        }
        return Result<void, U>();
    }

    // and_then - 链式操作
    template<typename F>
    [[nodiscard]] constexpr auto and_then(F&& f) const {
        using ResultType = std::invoke_result_t<F>;
        if (is_ok()) {
            return std::forward<F>(f)();
        }
        return ResultType(std::in_place_index<1>, std::get<1>(data_));
    }

    // or_else - 错误恢复
    template<typename F>
    [[nodiscard]] constexpr auto or_else(F&& f) const& {
        using ResultType = std::invoke_result_t<F, const E&>;
        if (is_err()) {
            return std::forward<F>(f)(std::get<1>(data_));
        }
        return ResultType();
    }

    // 移动版本的or_else
    template<typename F>
    [[nodiscard]] constexpr auto or_else(F&& f) && {
        using ResultType = std::invoke_result_t<F, E&&>;
        if (is_err()) {
            return std::forward<F>(f)(std::move(std::get<1>(data_)));
        }
        return ResultType();
    }

    // 比较操作符 - void 特化版本
    template<typename F>
    [[nodiscard]] constexpr bool operator==(const Result<void, F>& other) const {
        if (is_ok() && other.is_ok()) {
            return true;  // 两个都是成功状态
        }
        if (is_err() && other.is_err()) {
            return std::get<1>(data_) == std::get<1>(other.data_);
        }
        return false;
    }

    template<typename F>
    [[nodiscard]] constexpr bool operator!=(const Result<void, F>& other) const {
        return !(*this == other);
    }

    // 交换操作
    constexpr void swap(Result& other) noexcept(std::is_nothrow_swappable_v<E>) {
        data_.swap(other.data_);
    }

};

// Rust 风格的 Ok 和 Err 构造函数

// Ok 辅助类
template<typename T>
struct OkWrapper {
    T value;

    template<typename E>
    operator Result<T, E>() && {
        return Result<T, E>(std::move(value));
    }

    template<typename E>
    operator Result<T, E>() const& {
        return Result<T, E>(value);
    }
};

// Ok 构造函数 - 创建成功的 Result
template<typename T>
OkWrapper<std::decay_t<T>> Ok(T&& value) {
    return {std::forward<T>(value)};
}

// void 特化的 Ok 辅助类
struct VoidOkWrapper {
    template<typename E>
    operator Result<void, E>() const {
        return Result<void, E>();
    }
};

// void 的 Ok 构造函数
inline VoidOkWrapper Ok() {
    return {};
}

// Err 辅助类
template<typename E>
struct ErrWrapper {
    E error;

    template<typename T>
    operator Result<T, E>() && {
        return Result<T, E>(std::in_place_index<1>, std::move(error));
    }

    template<typename T>
    operator Result<T, E>() const& {
        return Result<T, E>(std::in_place_index<1>, error);
    }
};

// 基本 Err 构造函数 - 创建错误的 Result（通用转发版本，排除枚举类型）
template<typename E>
auto Err(E&& error) -> std::enable_if_t<!std::is_enum_v<std::decay_t<E>>, ErrWrapper<std::decay_t<E>>> {
    return {std::forward<E>(error)};
}

// Err 重载版本 - 通用 args 版本，用于就地构造错误对象
template<typename E, typename... Args>
ErrWrapper<E> Err(Args&&... args) {
    return {E(std::forward<Args>(args)...)};
}

// Err 重载版本 - 直接从错误枚举创建 ErrorCode（推荐用法）
template<typename EnumT>
auto Err(EnumT error_code)
    -> std::enable_if_t<std::is_enum_v<EnumT>, ErrWrapper<ErrorCode>> {
    // 使用 ADL 自动找到对应的 MakeErrorCode 函数
    return {ErrorCode(error_code)};
}


// 从异常创建错误Result的辅助函数
template<typename T, typename E = std::exception_ptr>
Result<T, E> from_exception(const std::exception& ex) {
    if constexpr (std::is_same_v<E, std::exception_ptr>) {
        return Result<T, E>(std::in_place_index<1>, std::make_exception_ptr(ex));
    } else {
        return Result<T, E>(std::in_place_index<1>, E(ex.what()));
    }
}

// 从当前异常创建错误Result的辅助函数
template<typename T, typename E = std::exception_ptr>
Result<T, E> from_current_exception() {
    if constexpr (std::is_same_v<E, std::exception_ptr>) {
        return Result<T, E>(std::in_place_index<1>, std::current_exception());
    } else {
        try {
            std::rethrow_exception(std::current_exception());
        } catch (const std::exception& ex) {
            return Result<T, E>(std::in_place_index<1>, E(ex.what()));
        } catch (...) {
            return Result<T, E>(std::in_place_index<1>, E("Unknown exception"));
        }
    }
}




// 全局 swap 函数以支持 ADL (Argument Dependent Lookup)
template<typename T, typename E>
constexpr void swap(Result<T, E>& lhs, Result<T, E>& rhs)
    noexcept(noexcept(lhs.swap(rhs))) {
    lhs.swap(rhs);
}

// 特化版本的全局 swap
template<typename E>
constexpr void swap(Result<void, E>& lhs, Result<void, E>& rhs)
    noexcept(noexcept(lhs.swap(rhs))) {
    lhs.swap(rhs);
}



}

#endif // RESULT_HPP_