cmake_minimum_required(VERSION 3.28)
find_package(PkgConfig REQUIRED)
pkg_check_modules(JEMALLOC REQUIRED jemalloc)
find_package(Threads REQUIRED)
find_package(spdlog REQUIRED)
#====================logger==============================
include_directories(log)


add_library(aether_utils SHARED
        log/log.cpp
        log/log.hpp
)


target_include_directories(aether_utils PUBLIC
        log
)
target_link_directories(aether_utils PUBLIC
        ${JEMALLOC_LIBRARY_DIRS}
)
target_link_libraries(aether_utils PUBLIC
        ${JEMALLOC_LIBRARIES}
        spdlog::spdlog
        Threads::Threads
)


#==================logger end ===========================