//
// Created by 15531 on 2025/4/21.
//
// log/log.cpp
#include "log.hpp"  // 包含我们自己的公共头文件

#include "log_impl.hpp"
// 包含所有需要的 spdlog 头文件
#include <ctime>
#include <exception>
#include <memory>
#include <spdlog/async.h>
#include <spdlog/async_logger.h>
#include <spdlog/common.h>
#include <spdlog/details/log_msg.h>
#include <spdlog/logger.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/spdlog.h>
// #include <spdlog/sinks/basic_file_sink.h> // 如果需要文件日志
#include <iostream>  // for std::cerr
#include <mutex>
#include <spdlog/fmt/fmt.h>
#include <spdlog/pattern_formatter.h>
#include <string>
#include <string_view>
#include <unordered_map>
#include <utility>

namespace ara::utils::log
{
    // ---------- Helper Function ----------
    // 将我们自己的 LogLevel 转换为 spdlog 的 level
    spdlog::level::level_enum to_spdlog_level(LogLevel level)
    {
        switch (level)
        {
            case LogLevel::Trace:
                return spdlog::level::trace;
            case LogLevel::Debug:
                return spdlog::level::debug;
            case LogLevel::Info:
                return spdlog::level::info;
            case LogLevel::Warn:
                return spdlog::level::warn;
            case LogLevel::Error:
                return spdlog::level::err;  // 注意 spdlog 使用 err
            case LogLevel::Critical:
                return spdlog::level::critical;
            case LogLevel::Off:
                return spdlog::level::off;
            default:
                return spdlog::level::info;  // 默认值
        }
    }


    // ---------- Logger 公共接口实现 ----------
    // 构造函数，创建 PIMPL 对象
    Logger::Logger(std::shared_ptr<LoggerImpl> impl) : pimpl_(std::move(impl)) {}

    // 析构函数定义 (必须在 LoggerImpl 完全定义后)
    Logger::~Logger() = default;

    // 拷贝构造
    Logger::Logger(const Logger& other) = default;
    // 移动构造
    Logger::Logger(Logger&& other) noexcept = default;
    // 拷贝赋值
    Logger& Logger::operator=(const Logger& other) = default;
    // 移动赋值
    Logger& Logger::operator=(Logger&& other) noexcept = default;

    // 实现 Logger 的公共方法，转发给 PIMPL
    void Logger::trace(std::string_view msg) const
    {
        if (pimpl_ == nullptr)
        {
            return;
        }
        pimpl_->trace(msg);
    }
    void Logger::debug(std::string_view msg) const
    {
        if (pimpl_ == nullptr)
        {
            return;
        }
        pimpl_->debug(msg);
    }
    void Logger::info(std::string_view msg) const
    {
        if (pimpl_ == nullptr)
        {
            return;
        }
        pimpl_->info(msg);
    }
    void Logger::warn(std::string_view msg) const
    {
        if (pimpl_ == nullptr)
        {
            return;
        }
        pimpl_->warn(msg);
    }
    void Logger::error(std::string_view msg) const
    {
        if (pimpl_ == nullptr)
        {
            return;
        }
        pimpl_->error(msg);
    }
    void Logger::critical(std::string_view msg) const
    {
        if (pimpl_ == nullptr)
        {
            return;
        }
        pimpl_->critical(msg);
    }
    const std::string& Logger::name() const
    {
        static const std::string empty_string;
        if (pimpl_ == nullptr)
        {
            return empty_string;
        }
        return pimpl_->name();
    }

    // 自定义日志格式化器
    class CustomFormatter : public spdlog::custom_flag_formatter
    {
    public:
        void format(const spdlog::details::log_msg& msg, const std::tm&, spdlog::memory_buf_t& dest) override
        {
            // 自定义格式化逻辑
            std::string level;
            switch (msg.level)
            {
                case spdlog::level::trace:
                    level = "TRACE";
                    break;
                case spdlog::level::debug:
                    level = "DEBUG";
                    break;
                case spdlog::level::info:
                    level = "INFO";
                    break;
                case spdlog::level::warn:
                    level = "WARN";
                    break;
                case spdlog::level::err:
                    level = "ERROR";
                    break;
                case spdlog::level::critical:
                    level = "FATAL";
                    break;
                default:
                    level = "UNKNW";
                    break;
            }

            dest.append(level.data(), level.data() + level.size());
        }

        [[nodiscard]] std::unique_ptr<custom_flag_formatter> clone() const override
        {
            return std::make_unique<CustomFormatter>();
        }
    };


    // ---------- LogSystem PIMPL 实现 ----------
    // LogSystemImpl 包含所有原始 LogSystem 的状态和逻辑
    class LogSystemImpl
    {
    public:
        LogSystemImpl() : initialized_(false) {}

        ~LogSystemImpl()
        {
            // 在 LogSystemImpl 析构时关闭 spdlog
            // 确保 LogSystem 的 pimpl_ 在 main 结束前被销毁
            if (initialized_)
            {
                try
                {
                    spdlog::shutdown();
                }
                catch (const std::exception& e)
                {
                    std::cerr << "Exception during spdlog shutdown: " << e.what() << std::endl;
                }
                catch (...)
                {
                    std::cerr << "Unknown exception during spdlog shutdown." << std::endl;
                }
            }
        }

        bool init(const LogConfig& config)
        {
            // 使用 std::call_once 保证线程安全的初始化
            std::call_once(
                init_flag_,
                [this, &config]()
                {
                    try
                    {
                        this->config_ = config;  // 保存配置

                        // 1. 创建 Sink(s)
                        console_sink_ = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();

                        // 2. 创建并设置 Formatter

                        // 创建并配置格式化器
                        auto formatter = std::make_unique<spdlog::pattern_formatter>();
                        formatter->add_flag<CustomFormatter>('L').set_pattern(config.pattern);


                        console_sink_->set_pattern(config.pattern);

                        // 初始化异步模式的线程池
                        if (config.async_mode)
                        {
                            spdlog::init_thread_pool(config.queue_size, config.thread_count);
                        }

                        // 4. 创建默认 Logger
                        std::shared_ptr<spdlog::logger> default_logger;
                        spdlog::sink_ptr sink = console_sink_;  // 使用 sink_ptr
                        if (config.async_mode)
                        {
                            default_logger = std::make_shared<spdlog::async_logger>(
                                "default",  // 默认 logger 名称
                                sink,  // 使用上面创建的 sink
                                spdlog::thread_pool(), spdlog::async_overflow_policy::block);
                        }
                        else
                        {
                            default_logger = std::make_shared<spdlog::logger>("default", sink);
                        }

                        // 5. 设置默认 Logger 和全局设置
                        default_logger->set_level(to_spdlog_level(config.level));
                        spdlog::set_default_logger(default_logger);
                        spdlog::set_level(to_spdlog_level(config.level));  // 全局级别
                        spdlog::flush_on(spdlog::level::err);  // 错误级别自动刷新

                        initialized_ = true;
                        // 使用刚创建的 logger 打印初始化成功信息
                        default_logger->info("aether Log system initialized successfully. Async={}", config.async_mode);
                    }
                    catch (const spdlog::spdlog_ex& ex)
                    {
                        std::cerr << "Log initialization failed (spdlog exception): " << ex.what() << std::endl;
                        initialized_ = false;
                    }
                    catch (const std::exception& e)
                    {
                        std::cerr << "Log initialization failed (std exception): " << e.what() << std::endl;
                        initialized_ = false;
                    }
                });
            return initialized_;
        }

        bool is_initialized() const { return initialized_; }

        // 获取或创建 spdlog logger
        std::shared_ptr<spdlog::logger> get_spdlog_logger(const std::string& name)
        {
            if (!initialized_)
            {
                // 惰性初始化：使用合理的默认配置
                // 符合 [SWS_CORE_15002] 规范，ara::core 类型可以独立使用
                LogConfig default_config;
                default_config.level = LogLevel::Info;
                default_config.async_mode = false;  // 同步模式更简单可靠
                default_config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%n] %v";
                
                init(default_config);
                if (!initialized_)
                {
                    // 如果初始化失败，返回 nullptr 而不是抛出异常
                    // 这样调用者可以安全地处理这种情况
                    return nullptr;
                }
            }

            std::lock_guard<std::mutex> lock(loggers_mutex_);
            auto logger = spdlog::get(name);  // 先尝试获取已注册的 logger

            if (!logger)
            {
                // 如果不存在，则创建新的 logger，复用已有的 sink
                spdlog::sink_ptr sink = console_sink_;
                if (config_.async_mode)
                {
                    logger = std::make_shared<spdlog::async_logger>(name, sink, spdlog::thread_pool(),
                                                                    spdlog::async_overflow_policy::block);
                }
                else
                {
                    logger = std::make_shared<spdlog::logger>(name, sink);
                }
                logger->set_level(to_spdlog_level(config_.level));  // 使用当前配置的级别
                spdlog::register_logger(logger);  // 注册新创建的 logger
            }
            return logger;
        }

        void set_level(LogLevel level)
        {
            if (initialized_)
            {
                config_.level = level;  // 更新保存的配置
                spdlog::set_level(to_spdlog_level(level));  // 设置全局级别
                // （可选）更新所有已创建 logger 的级别
                std::lock_guard<std::mutex> lock(loggers_mutex_);
                spdlog::apply_all([&](std::shared_ptr<spdlog::logger> l) { l->set_level(to_spdlog_level(level)); });
            }
        }

        void shutdown()
        {
            if (initialized_)
            {
                try
                {
                    spdlog::shutdown();
                    initialized_ = false;  // 标记为未初始化
                }
                catch (...)
                { /* Mutex exceptions */
                }
            }
        }

    private:
        std::once_flag init_flag_;
        bool initialized_;
        LogConfig config_;  // 保存抽象后的配置
        std::shared_ptr<spdlog::sinks::stdout_color_sink_mt> console_sink_;
        std::mutex loggers_mutex_;  // 保护 logger map (spdlog::get/register)
    };

    // ---------- LogSystem 公共接口实现 ----------
    // 静态实例获取
    LogSystem& LogSystem::instance()
    {
        // Meyers' Singleton: 线程安全 (C++11 onwards)
        static LogSystem instance;
        return instance;
    }

    LogSystem::LogSystem() : pimpl_(std::make_unique<LogSystemImpl>()) {}

    LogSystem::~LogSystem() = default;

    bool LogSystem::init(const LogConfig& config) { return pimpl_->init(config); }

    bool LogSystem::is_initialized() const { return pimpl_->is_initialized(); }

    // LogSystem::get_logger 实现需要调整为使用 LogSystemImpl 的方法
    Logger LogSystem::get_logger(const std::string& name)
    {
        if (!pimpl_ || !is_initialized())
        {
            // 返回一个无效的 Logger 或抛出异常
            // 最好是在 init() 失败时处理或确保 init() 先被调用
            // 返回一个空的 Logger 实例，其 pimpl_ 为 nullptr
            return Logger(nullptr);
        }
        // 调用 LogSystemImpl 来获取或创建底层的 spdlog logger
        auto spdlog_logger = pimpl_->get_spdlog_logger(name);
        // 创建 LoggerImpl
        auto logger_impl = std::make_shared<LoggerImpl>(std::move(spdlog_logger));
        // 返回包含 LoggerImpl 的 Logger 对象
        return Logger(std::move(logger_impl));
    }


    // --- 新增 GetLoggerForTag 实现 ---
    namespace
    {  // 使用匿名 namespace 限制作用域
        std::mutex logger_map_mutex;
        std::unordered_map<std::string, Logger> logger_map;
    }  // namespace

    Logger& GetLoggerForTag(const std::string& tag)
    {
        // 第一次检查（无锁），提高性能
        // 注意：这里可能存在竞态条件，但在多线程首次访问同一tag时，
        // 最坏情况是 LogSystem::instance().get_logger(tag) 被调用多次，
        // 但由于 get_logger 内部应该处理了 spdlog logger 的唯一性，
        // 并且我们最终只插入一次到 map 中，所以通常是安全的。
        // 如果 get_logger 代价很高或有副作用，则需要更严格的锁定。
        auto it = logger_map.find(tag);
        if (it != logger_map.end())
        {
            return it->second;
        }

        // 如果未找到，加锁进行第二次检查和可能的插入
        std::lock_guard<std::mutex> lock(logger_map_mutex);
        it = logger_map.find(tag); // 再次检查，防止其他线程已插入
        if (it == logger_map.end())
        {
            // 惰性初始化：确保 LogSystem 已初始化
            if (!LogSystem::instance().is_initialized())
            {
                // 使用默认配置进行惰性初始化
                // 这符合 [SWS_CORE_15002] 规范：ara::core 类型可以独立使用，无需显式初始化
                LogConfig default_config;
                default_config.level = LogLevel::Info;
                default_config.async_mode = false;  // 为了简单和可靠性，使用同步模式
                default_config.pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%n] %v";
                
                bool init_success = LogSystem::instance().init(default_config);
                if (!init_success)
                {
                     // 如果默认初始化失败，返回一个安全的空logger
                     // 避免抛出异常，确保日志调用永远不会导致程序崩溃
                     static Logger empty_logger(nullptr);
                     return empty_logger;
                }
            }
            // Logger 不存在，调用 LogSystem 获取/创建它并插入 map
            // emplace 返回 pair<iterator, bool>
            it = logger_map.emplace(tag, LogSystem::instance().get_logger(tag)).first;
        }
        return it->second; // 返回 map 中的 Logger 引用
    }

    // --- Logger 实现 ---
    void LogSystem::set_level(LogLevel level) { pimpl_->set_level(level); }

    void LogSystem::shutdown() { pimpl_->shutdown(); }

}  // namespace aether::Utils::log
