//
// Created by 15531 on 2025/4/21.
//

// log/log.hpp
#ifndef aether_LOG_HPP
#define aether_LOG_HPP

#include <string>
#include <string_view>
#include <memory>
#include "log_impl.hpp"
namespace aether::Utils::log
{
    enum class LogLevel {
        Trace,
        Debug,
        Info,
        Warn,
        Error,
        Critical,
        Off
    };


    struct LogConfig {
        LogLevel level = LogLevel::Info;
        size_t queue_size = 8192;      // 异步队列大小
        size_t thread_count = 1;     // 异步线程数：推荐设置为1
        bool async_mode = true;        // 是否启用异步模式
        // std::string pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%t] %v"; // %t for thread id
        std::string pattern = "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%n] %v";

    };

    class LogSystemImpl;

    // 公共 Logger 接口类
    class Logger
    {
    public:
        // 提供日志记录方法
        void trace(std::string_view msg) const;
        void debug(std::string_view msg) const;
        void info(std::string_view msg) const;
        void warn(std::string_view msg) const;
        void error(std::string_view msg) const;
        void critical(std::string_view msg) const;

        // 模板化的方法，支持 fmtlib 风格的格式化
        template <typename... Args>
        void trace(std::string_view fmt, Args&&... args)
        {
            if (pimpl_ == nullptr)return;
            pimpl_->trace(fmt, std::forward<Args>(args)...);
        }
        template <typename... Args>
        void debug(std::string_view fmt, Args&&... args)
        {
            if (pimpl_ == nullptr)return;
            pimpl_->debug(fmt, std::forward<Args>(args)...);
        }
        template <typename... Args>
        void info(std::string_view fmt, Args&&... args)
        {
            if (pimpl_ == nullptr)return;
            pimpl_->info(fmt, std::forward<Args>(args)...);
        }
        template <typename... Args>
        void warn(std::string_view fmt, Args&&... args)
        {
            if (pimpl_ == nullptr)return;
            pimpl_->warn(fmt, std::forward<Args>(args)...);
        }
        template <typename... Args>
        void error(std::string_view fmt, Args&&... args)
        {
            if (pimpl_ == nullptr)return;
            pimpl_->error(fmt, std::forward<Args>(args)...);
        }
        template <typename... Args>
        void critical(std::string_view fmt, Args&&... args)
        {
            if (pimpl_ == nullptr)return;
            pimpl_->critical(fmt, std::forward<Args>(args)...);
        }

        explicit Logger(std::shared_ptr<LoggerImpl> impl);
        ~Logger();
        Logger(const Logger& other);
        Logger(Logger&& other) noexcept;
        Logger& operator=(const Logger& other);
        Logger& operator=(Logger&& other) noexcept;

        [[nodiscard]] const std::string& name() const;

    private:
        std::shared_ptr<LoggerImpl> pimpl_;
    };



    // 5. 定义 LogSystem 单例接口 (PIMPL)
    class LogSystem {
    public:
        // 获取单例实例
        static LogSystem& instance();

        // 返回 true 表示初始化成功或已初始化
        bool init(const LogConfig& config = LogConfig{});

        // 检查是否已初始化
        bool is_initialized() const;

        // 获取命名日志记录器 (返回我们自己的 Logger 类)
        Logger get_logger(const std::string& name);

        // 设置全局日志级别 (使用抽象后的 LogLevel)
        void set_level(LogLevel level);

        // 关闭和清理日志系统（可选，通常在程序结束时自动处理）
        void shutdown();

        // 禁止拷贝和赋值
        LogSystem(const LogSystem&) = delete;
        LogSystem& operator=(const LogSystem&) = delete;

    private:
        // 私有构造函数和析构函数（由 instance 控制）
        LogSystem();
        ~LogSystem();

        // 指向实际实现的指针 (使用 unique_ptr 因为 LogSystem 是单例)
        std::unique_ptr<LogSystemImpl> pimpl_;
    };

    // 添加获取 Logger 的函数声明
    Logger& GetLoggerForTag(const std::string& tag);

    // 修改日志记录宏，增加 tag 参数
    #define LOG_TRACE(tag, ...) do { ::aether::Utils::log::GetLoggerForTag(tag).trace(__VA_ARGS__); } while(0)
    #define LOG_DEBUG(tag, ...) do { ::aether::Utils::log::GetLoggerForTag(tag).debug(__VA_ARGS__); } while(0)
    #define LOG_INFO(tag, ...)  do { ::aether::Utils::log::GetLoggerForTag(tag).info(__VA_ARGS__); } while(0)
    #define LOG_WARN(tag, ...)  do { ::aether::Utils::log::GetLoggerForTag(tag).warn(__VA_ARGS__); } while(0)
    #define LOG_ERROR(tag, ...) do { ::aether::Utils::log::GetLoggerForTag(tag).error(__VA_ARGS__); } while(0)
    #define LOG_CRITICAL(tag, ...) do { ::aether::Utils::log::GetLoggerForTag(tag).critical(__VA_ARGS__); } while(0)


} // namespace aether::Utils::log




#endif // aether_LOG_HPP

