//
// Created by 15531 on 2025/4/22.
//

#ifndef LOG_IMPL_HPP
#define LOG_IMPL_HPP
#include <string_view>
#include <spdlog/spdlog.h>
namespace ara::utils::log
{
// ---------- <PERSON>gger PIMPL 实现 ----------
    // LoggerImpl 包含实际的 spdlog logger
    class LoggerImpl
    {
    public:
        // 构造函数，持有 spdlog logger
        explicit LoggerImpl(std::shared_ptr<spdlog::logger> logger) : spdlog_logger_(std::move(logger)) {}

        // 提供与 Logger 公共接口对应的实现方法
        void trace(std::string_view msg) const { spdlog_logger_->trace(msg); }
        void debug(std::string_view msg) const { spdlog_logger_->debug(msg); }
        void info(std::string_view msg) const { spdlog_logger_->info(msg); }
        void warn(std::string_view msg) const { spdlog_logger_->warn(msg); }
        void error(std::string_view msg) const { spdlog_logger_->error(msg); }
        void critical(std::string_view msg) const { spdlog_logger_->critical(msg); }

        // 模板化的日志方法实现
        template <typename... Args>
        void trace(std::string_view fmt, Args&&... args)
        {
            spdlog_logger_->trace(fmt::runtime(fmt), std::forward<Args>(args)...);
        }
        template <typename... Args>
        void debug(std::string_view fmt, Args&&... args)
        {
            spdlog_logger_->debug(fmt::runtime(fmt), std::forward<Args>(args)...);
        }
        template <typename... Args>
        void info(std::string_view fmt, Args&&... args)
        {
            spdlog_logger_->info(fmt::runtime(fmt), std::forward<Args>(args)...);
        }
        template <typename... Args>
        void warn(std::string_view fmt, Args&&... args)
        {
            spdlog_logger_->warn(fmt::runtime(fmt), std::forward<Args>(args)...);
        }
        template <typename... Args>
        void error(std::string_view fmt, Args&&... args)
        {
            spdlog_logger_->error(fmt::runtime(fmt), std::forward<Args>(args)...);
        }
        template <typename... Args>
        void critical(std::string_view fmt, Args&&... args)
        {
            spdlog_logger_->critical(fmt::runtime(fmt), std::forward<Args>(args)...);
        }

        const std::string& name() const { return spdlog_logger_->name(); }

        // 返回内部的 spdlog logger (如果 LogSystemImpl 需要访问)
        std::shared_ptr<spdlog::logger> get_spdlog_logger() const { return spdlog_logger_; }

    private:
        std::shared_ptr<spdlog::logger> spdlog_logger_;
    };

}

#endif //LOG_IMPL_HPP
