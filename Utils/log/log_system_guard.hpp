#ifndef aether_LOG_SYSTEM_GUARD_HPP
#define aether_LOG_SYSTEM_GUARD_HPP

#include "log.hpp"

namespace aether::Utils::log
{

class LogSystemGuard
{
public:
    explicit LogSystemGuard(const LogConfig& config = LogConfig{})
    {
        LogSystem::instance().init(config);
    }

    ~LogSystemGuard()
    {
        LogSystem::instance().shutdown();
    }

    LogSystemGuard(const LogSystemGuard&) = delete;
    LogSystemGuard& operator=(const LogSystemGuard&) = delete;
};

} // namespace aether::Utils::log

#endif // aether_LOG_SYSTEM_GUARD_HPP