# FastDDSIDL.cmake
# 用于处理 FastDDS IDL 文件的 CMake 模块，在配置阶段生成代码

# 查找 fastddsgen 工具
find_program(FASTDDSGEN_BIN fastddsgen)
if(NOT FASTDDSGEN_BIN)
    message(FATAL_ERROR "fastddsgen 未找到，请确保它在 PATH 中")
endif()

# 为一组 IDL 文件生成代码的函数（在配置阶段）
# 参数:
# - TARGET: 目标名称（将在其上设置属性）
# - IDL_DIR: IDL 文件所在的目录
# - OUTPUT_DIR: 生成代码的顶级输出目录
# - IDLS: IDL 文件列表（相对于 IDL_DIR 的路径）
function(fastdds_generate_from_idl_at_configure)
    # 解析参数
    cmake_parse_arguments(ARG 
        "REPLACE;EXAMPLE;TYPEOBJECT" 
        "TARGET;IDL_DIR;OUTPUT_DIR" 
        "IDLS;EXTRA_ARGS" ${ARGN})

    if(NOT DEFINED ARG_TARGET OR NOT DEFINED ARG_IDL_DIR OR NOT DEFINED ARG_OUTPUT_DIR OR NOT DEFINED ARG_IDLS)
        message(FATAL_ERROR "fastdds_generate_from_idl_at_configure 需要 TARGET、IDL_DIR、OUTPUT_DIR 和 IDLS 参数")
    endif()

    # 确保输出目录存在
    file(MAKE_DIRECTORY ${ARG_OUTPUT_DIR})

    # 生成的所有文件的列表
    set(GENERATED_FILES "")
    set(GENERATED_DIRS "")
    
    # 基本命令参数
    # 注意：FastDDS生成器的行为不统一，我们将使用临时目录避免混乱
    set(TEMP_OUTPUT_DIR "${CMAKE_CURRENT_BINARY_DIR}/fastdds_temp")
    file(MAKE_DIRECTORY ${TEMP_OUTPUT_DIR})
    set(FASTDDSGEN_ARGS -d ${TEMP_OUTPUT_DIR})
    
    # 添加条件选项
    if(ARG_REPLACE)
        list(APPEND FASTDDSGEN_ARGS -replace)
    endif()
    
    if(ARG_EXAMPLE)
        list(APPEND FASTDDSGEN_ARGS -example)
    endif()
    
    if(ARG_TYPEOBJECT)
        list(APPEND FASTDDSGEN_ARGS -typeobject)
    endif()
    
    # 添加额外自定义参数
    if(DEFINED ARG_EXTRA_ARGS)
        list(APPEND FASTDDSGEN_ARGS ${ARG_EXTRA_ARGS})
    endif()
    
    # 选择性更新：检查是否需要重新生成代码
    set(NEED_REGENERATE FALSE)
    
    # 分别处理每个IDL文件
    foreach(IDL_FILE ${ARG_IDLS})
        # 获取 IDL 文件名（不带路径和后缀）
        get_filename_component(IDL_NAME ${IDL_FILE} NAME_WE)
        get_filename_component(IDL_NAME_LOWER ${IDL_FILE} NAME_WE)
        string(TOLOWER ${IDL_NAME_LOWER} IDL_NAME_LOWER)
        
        # 完整的 IDL 文件路径
        set(FULL_IDL_PATH "${ARG_IDL_DIR}/${IDL_FILE}")
        
        # 最终输出目录（每个IDL一个子目录）
        set(OUTPUT_SUBDIR "${ARG_OUTPUT_DIR}/${IDL_NAME_LOWER}")
        list(APPEND GENERATED_DIRS "${OUTPUT_SUBDIR}")
        
        # 查找可能的生成文件模式
        set(POSSIBLE_EXTENSIONS
            ".cxx" ".cpp" ".hpp" ".h" 
            "PubSubTypes.cxx" "PubSubTypes.cpp" "PubSubTypes.hpp" "PubSubTypes.h"
            "TypeObject.cpp" "TypeObject.hpp" 
            "TypeObjectSupport.cxx" "TypeObjectSupport.cpp"
            "TypeObjectSupport.hpp" "TypeObjectSupport.h"
            "CdrAux.hpp" "CdrAux.ipp"
        )
        
        # 检测至少一个预期文件是否存在
        set(MARKER_FILE "${OUTPUT_SUBDIR}/${IDL_NAME_LOWER}.hpp")
        if(NOT EXISTS "${MARKER_FILE}" OR "${FULL_IDL_PATH}" IS_NEWER_THAN "${MARKER_FILE}")
            set(NEED_REGENERATE TRUE)
        endif()
    endforeach()
    
    # 如果需要重新生成，或者是第一次运行，就生成所有文件
    if(NEED_REGENERATE)
        message(STATUS "生成 FastDDS 代码从 IDL 文件夹: ${ARG_IDL_DIR}")
        
        # 清理临时目录
        file(REMOVE_RECURSE ${TEMP_OUTPUT_DIR})
        file(MAKE_DIRECTORY ${TEMP_OUTPUT_DIR})
        
        # 分别处理每个IDL文件
        foreach(IDL_FILE ${ARG_IDLS})
            # 获取 IDL 文件名（不带路径和后缀）
            get_filename_component(IDL_NAME ${IDL_FILE} NAME_WE)
            get_filename_component(IDL_NAME_LOWER ${IDL_FILE} NAME_WE)
            string(TOLOWER ${IDL_NAME_LOWER} IDL_NAME_LOWER)
            
            # 完整的 IDL 文件路径
            set(FULL_IDL_PATH "${ARG_IDL_DIR}/${IDL_FILE}")
            
            # 最终输出目录（每个IDL一个子目录）
            set(OUTPUT_SUBDIR "${ARG_OUTPUT_DIR}/${IDL_NAME_LOWER}")
            file(MAKE_DIRECTORY ${OUTPUT_SUBDIR})
            
            # 在配置阶段执行 fastddsgen
            message(STATUS "处理 IDL 文件: ${IDL_FILE}")
            execute_process(
                COMMAND ${FASTDDSGEN_BIN} ${FASTDDSGEN_ARGS} ${FULL_IDL_PATH}
                RESULT_VARIABLE FASTDDS_RESULT
                OUTPUT_VARIABLE FASTDDS_OUTPUT
                ERROR_VARIABLE FASTDDS_ERROR
                WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
            )
            
            # 检查结果
            if(NOT FASTDDS_RESULT EQUAL 0)
                message(FATAL_ERROR "fastddsgen 执行失败: ${FASTDDS_ERROR}")
            endif()
            
            # 查找生成的所有文件，并移动到正确的位置
            file(GLOB_RECURSE GENERATED_FILES_TEMP
                 "${TEMP_OUTPUT_DIR}/*${IDL_NAME_LOWER}*"
                 "${TEMP_OUTPUT_DIR}/*${IDL_NAME}*")
            
            # 复制文件到最终目录，并记录生成的文件
            foreach(GEN_FILE ${GENERATED_FILES_TEMP})
                get_filename_component(GEN_FILE_NAME ${GEN_FILE} NAME)
                set(DEST_FILE "${OUTPUT_SUBDIR}/${GEN_FILE_NAME}")
                file(COPY ${GEN_FILE} DESTINATION ${OUTPUT_SUBDIR})
                list(APPEND GENERATED_FILES ${DEST_FILE})
                message(STATUS "生成文件: ${GEN_FILE_NAME} -> ${OUTPUT_SUBDIR}")
            endforeach()
            
            # 检查是否有文件生成
            if(NOT GENERATED_FILES_TEMP)
                message(WARNING "FastDDS 未生成任何文件: ${IDL_FILE}")
            endif()
        endforeach()
        
        # 清理临时目录
        file(REMOVE_RECURSE ${TEMP_OUTPUT_DIR})
    else()
        message(STATUS "FastDDS 生成的代码是最新的，跳过生成")
        
        # 虽然跳过生成，但我们仍需要收集已生成的文件列表
        foreach(IDL_FILE ${ARG_IDLS})
            get_filename_component(IDL_NAME ${IDL_FILE} NAME_WE)
            get_filename_component(IDL_NAME_LOWER ${IDL_FILE} NAME_WE)
            string(TOLOWER ${IDL_NAME_LOWER} IDL_NAME_LOWER)
            
            set(OUTPUT_SUBDIR "${ARG_OUTPUT_DIR}/${IDL_NAME_LOWER}")
            
            file(GLOB_RECURSE SUBDIR_FILES "${OUTPUT_SUBDIR}/*")
            list(APPEND GENERATED_FILES ${SUBDIR_FILES})
        endforeach()
    endif()
    
    # 设置在父作用域中需要的变量
    set(${ARG_TARGET}_INCLUDE_DIR ${ARG_OUTPUT_DIR} PARENT_SCOPE)
    set(${ARG_TARGET}_GENERATED_FILES ${GENERATED_FILES} PARENT_SCOPE)
    set(${ARG_TARGET}_GENERATED_DIRS ${GENERATED_DIRS} PARENT_SCOPE)
    
    # 为目标创建一个接口库，以管理生成的代码
    add_library(${ARG_TARGET} INTERFACE)
    target_include_directories(${ARG_TARGET} INTERFACE ${ARG_OUTPUT_DIR})
    
    # 将生成的文件标记为 GENERATED，并添加到源码组中（对 IDE 有用）
    foreach(GENERATED_FILE ${GENERATED_FILES})
        if(EXISTS ${GENERATED_FILE})
            set_source_files_properties(${GENERATED_FILE} PROPERTIES GENERATED TRUE)
            
            # 获取相对路径，用于源码组
            file(RELATIVE_PATH REL_PATH ${ARG_OUTPUT_DIR} ${GENERATED_FILE})
            get_filename_component(GROUP_DIR ${REL_PATH} DIRECTORY)
            if(GROUP_DIR)
                source_group("Generated Files\\${GROUP_DIR}" FILES ${GENERATED_FILE})
            else()
                source_group("Generated Files" FILES ${GENERATED_FILE})
            endif()
        endif()
    endforeach()
endfunction()

# 创建链接到生成代码的库
function(fastdds_create_library_from_idl)
    cmake_parse_arguments(ARG "" "TARGET;IDL_TARGET" "SOURCES" ${ARGN})
    
    if(NOT DEFINED ARG_TARGET OR NOT DEFINED ARG_IDL_TARGET)
        message(FATAL_ERROR "fastdds_create_library_from_idl 需要 TARGET 和 IDL_TARGET 参数")
    endif()
    
    # 获取生成的文件
    set(GENERATED_FILES ${${ARG_IDL_TARGET}_GENERATED_FILES})
    
    # 创建库
    add_library(${ARG_TARGET} SHARED ${GENERATED_FILES} ${ARG_SOURCES})
    
    # 添加包含目录
    target_include_directories(${ARG_TARGET} PUBLIC 
        ${${ARG_IDL_TARGET}_INCLUDE_DIR}
    )
    
    # 链接接口库（仅用于管理依赖关系）
    target_link_libraries(${ARG_TARGET} PUBLIC 
        ${ARG_IDL_TARGET}
        fastrtps
        fastcdr
    )
endfunction()
