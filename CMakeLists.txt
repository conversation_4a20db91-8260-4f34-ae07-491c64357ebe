cmake_minimum_required(VERSION 3.28)
project(aether_AA)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)


# # 使用现代CMake目标方式查找Boost
# # 注意：Asio是header-only库，包含在Boost::boost中
# # 但某些功能需要system库支持
# find_package(Boost CONFIG REQUIRED COMPONENTS
#     system          # Asio的某些功能需要，错误处理
#     filesystem      # 文件系统操作
#     thread          # 多线程支持
#     program_options # 命令行参数解析
#     regex           # 正则表达式
#     serialization   # 序列化
# )

# # 调试信息
# if(Boost_FOUND)
#     message(STATUS "Boost found: ${Boost_VERSION}")
#     message(STATUS "Boost include dirs: ${Boost_INCLUDE_DIRS}")
#     if(TARGET Boost::system)
#         message(STATUS "Boost::system target available")
#     endif()
# endif()

#ara::com
add_subdirectory(Utils)
add_subdirectory(Core)
add_subdirectory(Com)
# add_subdirectory(Application)
