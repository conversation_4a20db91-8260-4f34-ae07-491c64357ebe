cmake_minimum_required(VERSION 3.20)
project(Application)
include(../cmake/FastDDSGen.cmake)

set(idl_dir ${CMAKE_CURRENT_LIST_DIR}/idl)
set(generate_dir ${CMAKE_CURRENT_LIST_DIR}/Gen)
set(idl_files
    helloworld.idl
)

fastdds_generate_from_idl_at_configure(
        TARGET hellowolrd_gen
        IDL_DIR ${idl_dir}
        OUTPUT_DIR ${generate_dir}
        IDLS ${idl_files}
)

file(GLOB_RECURSE helloworld_gen_source Gen/helloworld/*.cxx)

add_executable(wrapper_pub src/Publisher.cpp ${helloworld_gen_source})
target_link_libraries(wrapper_pub aetherCom hellowolrd_gen)
target_link_directories(wrapper_pub PUBLIC ./Gen/helloworld)

add_executable(wrapper_sub src/Subscriber.cpp ${helloworld_gen_source})
target_link_libraries(wrapper_sub aetherCom hellowolrd_gen)
target_link_directories(wrapper_sub PUBLIC ./Gen/helloworld)